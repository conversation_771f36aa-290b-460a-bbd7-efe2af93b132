# Education RAG Management System

> 🎓 现代化的知识库管理平台，基于 RAG 技术的智能教育问答系统

[![Version](https://img.shields.io/badge/version-0.1.0--alpha-blue.svg)](https://github.com/your-repo/education-rag)
[![Tests](https://img.shields.io/badge/tests-75.6%25%20passing-yellow.svg)](./docs/TESTING-REPORT.md)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)

## 🚀 项目状态

**当前版本**: v0.1.0-alpha  
**完成度**: 35%  
**最后更新**: 2025-07-15

### ✅ 已实现功能
- 🐳 完整的 Docker 开发环境 (PostgreSQL + Redis)
- 🎨 响应式前端界面 (React + Next.js + Tailwind)
- 🔧 RESTful API 后端 (FastAPI + SQLAlchemy)
- 📝 知识库创建和管理界面
- 🧪 全面的端到端测试 (Playwright, 75.6% 通过率)

### 🔄 进行中
- 🐛 修复 SQLAlchemy 模型关系映射问题
- 📱 移动端兼容性优化

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   Next.js       │◄──►│   FastAPI       │◄──►│   PostgreSQL    │
│   React         │    │   SQLAlchemy    │    │   Redis         │
│   Tailwind CSS  │    │   Pydantic      │    │   Pinecone      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心技术栈
- **前端**: Next.js 14, TypeScript, Tailwind CSS, shadcn/ui
- **后端**: Python FastAPI, SQLAlchemy, Pydantic
- **数据库**: PostgreSQL, Redis, Pinecone
- **AI/ML**: LangChain, 智谱AI, DeepSeek
- **测试**: Playwright, Jest
- **部署**: Docker, Docker Compose

## 🚀 快速开始

### 前置要求
- Node.js 18+
- Python 3.11+
- Docker & Docker Compose
- Git

### 1. 克隆项目
```bash
git clone <repository-url>
cd education-rag
```

### 2. 启动数据库服务
```bash
# 启动 PostgreSQL 和 Redis
docker-compose up -d
```

### 3. 配置环境变量
```bash
# 复制环境变量模板
cp apps/backend/.env.example apps/backend/.env
cp apps/frontend/.env.example apps/frontend/.env

# 编辑配置文件，填入你的 API 密钥
# - Pinecone API Key
# - 智谱AI API Key  
# - DeepSeek API Key
```

### 4. 启动后端服务
```bash
cd apps/backend
pip install -r requirements.txt
cd src && python -m uvicorn education_rag_backend.main:app --reload
```

### 5. 启动前端服务
```bash
cd apps/frontend
npm install
npm run dev
```

### 6. 访问应用
- 前端: http://localhost:3001
- 后端 API: http://localhost:8000
- API 文档: http://localhost:8000/docs

## 🧪 运行测试

### 端到端测试
```bash
# 安装 Playwright
npx playwright install

# 运行测试
npx playwright test

# 查看测试报告
npx playwright show-report
```

### 单元测试
```bash
# 前端测试
cd apps/frontend
npm test

# 后端测试
cd apps/backend
pytest
```

## 📁 项目结构

```
education-rag/
├── apps/
│   ├── frontend/          # Next.js 前端应用
│   │   ├── src/
│   │   │   ├── app/       # App Router 页面
│   │   │   ├── components/ # React 组件
│   │   │   └── lib/       # 工具函数
│   │   └── package.json
│   └── backend/           # FastAPI 后端应用
│       ├── src/
│       │   └── education_rag_backend/
│       │       ├── api/   # API 路由
│       │       ├── models/ # 数据模型
│       │       └── services/ # 业务逻辑
│       └── requirements.txt
├── docs/                  # 项目文档
├── tests/                 # Playwright 测试
├── data/                  # 测试数据
├── docker-compose.yml     # Docker 配置
└── init.sql              # 数据库初始化
```

## 📖 文档

- [项目概览](./docs/PROJECT-OVERVIEW.md) - 详细的项目介绍和架构说明
- [实现状态](./docs/IMPLEMENTATION-STATUS.md) - 当前功能实现状态追踪
- [测试报告](./docs/TESTING-REPORT.md) - 详细的测试结果和分析
- [API 文档](http://localhost:8000/docs) - 自动生成的 API 文档

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📋 开发计划

### 🎯 短期目标 (1-2周)
- [ ] 修复 SQLAlchemy 关系映射问题
- [ ] 完善知识库管理功能
- [ ] 实现文档上传功能

### 🎯 中期目标 (3-4周)
- [ ] 文档处理和向量化
- [ ] RAG 检索功能
- [ ] 智能问答对话

### 🎯 长期目标 (1-2个月)
- [ ] 用户认证和权限管理
- [ ] 高级搜索功能
- [ ] 性能优化和监控

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [LangChain](https://langchain.com/) - RAG 框架支持
- [Pinecone](https://pinecone.io/) - 向量数据库服务
- [智谱AI](https://zhipuai.cn/) - 文本嵌入服务
- [DeepSeek](https://deepseek.com/) - 大语言模型服务

---

**维护者**: [Your Name](mailto:<EMAIL>)  
**最后更新**: 2025-07-15
