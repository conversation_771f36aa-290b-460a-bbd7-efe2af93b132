// 统一的文本内容管理文件
// 所有UI中显示的文本都在这里定义，方便维护和国际化

export const texts = {
  // 通用文本
  common: {
    appName: 'RAG管理系统',
    appShortName: 'RAG',
    loading: '加载中...',
    error: '错误',
    success: '成功',
    cancel: '取消',
    confirm: '确认',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    create: '创建',
    search: '搜索',
    filter: '筛选',
    all: '全部',
    public: '公开',
    private: '私有',
    settings: '设置',
    profile: '个人资料',
    logout: '退出登录',
    login: '登录',
    register: '注册',
    getStarted: '开始使用',
    viewDocs: '查看文档',
    startBuilding: '开始构建',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    close: '关闭',
    open: '打开',
    upload: '上传',
    download: '下载',
    copy: '复制',
    share: '分享',
    more: '更多',
  },

  // 主页文本
  home: {
    heroTitleHighlight: 'RAG管理系统',
    
    // 功能特性
    features: {
      knowledgeBase: {
        title: '知识库管理',
        description: '通过直观的控制界面创建和管理多个知识库',
        items: [
          '多知识库支持',
          '基于角色的权限管理',
          '模板配置',
          '基于标签的组织'
        ]
      },
      documentProcessing: {
        title: '文档处理',
        description: '先进的文档解析和智能分块技术',
        items: [
          '多格式支持 (PDF, DOCX, TXT)',
          '扫描文档OCR识别',
          '智能分块策略',
          '实时处理状态'
        ]
      },
      retrievalTesting: {
        title: '检索测试',
        description: '实时测试和优化您的检索质量',
        items: [
          '交互式查询测试',
          '相关性评分分析',
          'A/B测试支持',
          '性能指标监控'
        ]
      }
    },

    footer: {
      copyright: '© 2025 教育RAG管理系统。为知识管理的未来而构建。'
    }
  },

  // 知识库页面文本
  knowledgeBases: {
    title: '知识库',
    description: '管理您的文档集合和知识仓库',
    createButton: '创建知识库',
    searchPlaceholder: '搜索知识库...',
    
    // 筛选器
    filters: {
      all: '全部',
      public: '公开',
      private: '私有'
    },

    // 卡片信息
    card: {
      documents: '个文档',
      owner: '所有者',
      updated: '更新于',
      public: '公开',
      private: '私有'
    },

    // 空状态
    empty: {
      title: '未找到知识库',
      searchDescription: '尝试调整您的搜索条件',
      createDescription: '开始创建您的第一个知识库',
      createButton: '创建知识库'
    },

    // 创建知识库
    create: {
      title: '创建知识库',
      description: '创建一个新的知识库来管理您的文档和知识',
      formTitle: '知识库信息',
      validation: {
        nameRequired: '知识库名称不能为空',
        nameMaxLength: '知识库名称不能超过255个字符'
      },
      form: {
        name: '名称',
        namePlaceholder: '输入知识库名称',
        nameDescription: '为您的知识库起一个简短而有描述性的名称',
        description: '描述',
        descriptionPlaceholder: '输入知识库描述',
        descriptionDescription: '简要描述这个知识库的用途和内容',
        tags: '标签',
        tagsPlaceholder: '添加标签',
        tagsDescription: '添加标签以便更好地组织和查找知识库',
        isPublic: '公开知识库',
        isPublicDescription: '公开的知识库可以被所有用户访问',
        submit: '创建知识库'
      },
      documents: {
        title: '上传文档',
        description: '选择要添加到知识库的文档',
        uploadButton: '上传文档',
        optional: '（可选）'
      },
      success: {
        title: '创建成功',
        description: '知识库已成功创建'
      },
      error: {
        title: '创建失败',
        description: '创建知识库时出现错误，请稍后重试'
      }
    }
  },

  // 文档管理文本
  documents: {
    title: '文档管理',
    description: '管理知识库中的文档',
    uploadButton: '上传文档',
    bulkActions: '批量操作',
    viewMode: '查看模式',
    
    // 文档状态
    status: {
      pending: '等待处理',
      processing: '处理中',
      completed: '已完成',
      failed: '处理失败',
      retrying: '重试中'
    },

    // 文档操作
    actions: {
      preview: '预览',
      delete: '删除',
      reprocess: '重新处理',
      download: '下载'
    },

    // 上传
    upload: {
      dragDrop: '拖拽文件到此处或点击上传',
      supportedFormats: '支持的格式：PDF, DOCX, DOC, TXT, MD',
      maxSize: '最大文件大小：100MB',
      processing: '正在处理文档...',
      success: '文档上传成功',
      error: '文档上传失败'
    }
  },

  // 搜索测试文本
  search: {
    title: '检索测试',
    description: '测试和优化您的检索质量',
    queryPlaceholder: '输入您的查询...',
    searchButton: '搜索',
    
    // 参数配置
    parameters: {
      title: '搜索参数',
      topK: '返回结果数量',
      threshold: '相似度阈值',
      strategy: '检索策略',
      filters: '过滤条件'
    },

    // 结果显示
    results: {
      title: '搜索结果',
      relevanceScore: '相关性评分',
      sourceDocument: '来源文档',
      page: '第{page}页',
      noResults: '未找到相关结果',
      searchTime: '搜索耗时',
      totalResults: '共找到{count}个结果'
    }
  },

  // 用户认证文本
  auth: {
    login: {
      title: '登录',
      email: '邮箱',
      password: '密码',
      loginButton: '登录',
      forgotPassword: '忘记密码？',
      noAccount: '还没有账户？',
      signUp: '注册'
    },
    
    register: {
      title: '注册',
      fullName: '姓名',
      email: '邮箱',
      password: '密码',
      confirmPassword: '确认密码',
      registerButton: '注册',
      hasAccount: '已有账户？',
      signIn: '登录'
    }
  },

  // 错误信息
  errors: {
    networkError: '网络连接错误，请检查您的网络连接',
    serverError: '服务器错误，请稍后重试',
    unauthorized: '未授权访问，请先登录',
    forbidden: '权限不足，无法执行此操作',
    notFound: '请求的资源不存在',
    validationError: '输入数据验证失败',
    fileUploadError: '文件上传失败',
    fileSizeError: '文件大小超出限制',
    fileTypeError: '不支持的文件类型',
    unknownError: '未知错误，请联系管理员'
  },

  // 成功信息
  success: {
    knowledgeBaseCreated: '知识库创建成功',
    knowledgeBaseUpdated: '知识库更新成功',
    knowledgeBaseDeleted: '知识库删除成功',
    documentUploaded: '文档上传成功',
    documentDeleted: '文档删除成功',
    settingsSaved: '设置保存成功',
    profileUpdated: '个人资料更新成功'
  },

  // 确认对话框
  confirmations: {
    deleteKnowledgeBase: '确定要删除这个知识库吗？此操作无法撤销。',
    deleteDocument: '确定要删除这个文档吗？此操作无法撤销。',
    deleteMultipleDocuments: '确定要删除选中的{count}个文档吗？此操作无法撤销。',
    leaveWithoutSaving: '您有未保存的更改，确定要离开吗？'
  },

  // 时间格式
  time: {
    justNow: '刚刚',
    minutesAgo: '{minutes}分钟前',
    hoursAgo: '{hours}小时前',
    daysAgo: '{days}天前',
    weeksAgo: '{weeks}周前',
    monthsAgo: '{months}个月前',
    yearsAgo: '{years}年前'
  },

  // 文件大小格式
  fileSize: {
    bytes: '字节',
    kb: 'KB',
    mb: 'MB',
    gb: 'GB',
    tb: 'TB'
  }
} as const;

// 类型定义，确保类型安全
export type TextKeys = typeof texts;
export type CommonTextKeys = keyof typeof texts.common;
export type HomeTextKeys = keyof typeof texts.home;
export type KnowledgeBasesTextKeys = keyof typeof texts.knowledgeBases;

// 辅助函数：获取文本内容
export function getText(path: string): string {
  const keys = path.split('.');
  let current: any = texts;
  
  for (const key of keys) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      console.warn(`Text key not found: ${path}`);
      return path; // 返回原始路径作为fallback
    }
  }
  
  return typeof current === 'string' ? current : path;
}

// 辅助函数：格式化带参数的文本
export function formatText(template: string, params: Record<string, string | number>): string {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return params[key]?.toString() || match;
  });
}

// 辅助函数：格式化文件大小为中文
export function formatBytesZh(bytes: number, decimals = 2): string {
  if (bytes === 0) return '0 字节';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['字节', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
