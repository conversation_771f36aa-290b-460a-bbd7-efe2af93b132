# 文本内容管理系统

## 概述

`texts.ts` 文件是整个前端应用的文本内容管理中心，所有UI中显示的中文文本都在这里统一定义和管理。

## 设计理念

### 1. 统一管理
- 所有文本内容集中在一个文件中
- 便于维护和更新
- 避免重复定义

### 2. 类型安全
- 使用TypeScript确保类型安全
- 提供智能提示和错误检查
- 防止拼写错误

### 3. 易于扩展
- 模块化的结构设计
- 支持嵌套对象组织
- 便于添加新的文本内容

## 文件结构

```typescript
export const texts = {
  // 通用文本
  common: {
    appName: '教育RAG管理系统',
    login: '登录',
    // ...
  },

  // 主页文本
  home: {
    heroTitle: '全面的RAG',
    features: {
      knowledgeBase: {
        title: '知识库管理',
        // ...
      }
    }
  },

  // 其他页面文本...
}
```

## 使用方法

### 1. 基本使用

```typescript
import { texts } from '@/lib/texts';

// 在组件中使用
<h1>{texts.common.appName}</h1>
<Button>{texts.common.login}</Button>
```

### 2. 动态文本

```typescript
import { formatText } from '@/lib/texts';

// 带参数的文本
const message = formatText(texts.time.minutesAgo, { minutes: 5 });
// 结果: "5分钟前"
```

### 3. 条件文本

```typescript
// 根据条件显示不同文本
{isPublic ? texts.knowledgeBases.card.public : texts.knowledgeBases.card.private}
```

## 文本分类

### common (通用文本)
- 应用名称
- 基本操作按钮
- 通用状态文本

### home (主页文本)
- 英雄区域内容
- 功能特性描述
- 技术栈展示

### knowledgeBases (知识库页面)
- 页面标题和描述
- 搜索和筛选
- 卡片信息
- 空状态提示

### documents (文档管理)
- 文档状态
- 操作按钮
- 上传相关

### search (搜索测试)
- 查询界面
- 参数配置
- 结果展示

### auth (用户认证)
- 登录注册表单
- 验证信息

### errors (错误信息)
- 各种错误提示
- 网络错误
- 验证错误

### success (成功信息)
- 操作成功提示
- 保存成功等

## 辅助函数

### getText(path: string)
根据路径获取文本内容，支持嵌套访问。

```typescript
const title = getText('home.features.knowledgeBase.title');
// 结果: "知识库管理"
```

### formatText(template: string, params: object)
格式化带参数的文本模板。

```typescript
const text = formatText('共找到{count}个结果', { count: 10 });
// 结果: "共找到10个结果"
```

### formatBytesZh(bytes: number)
将字节数格式化为中文单位。

```typescript
const size = formatBytesZh(1048576);
// 结果: "1 MB"
```

## 最佳实践

### 1. 命名规范
- 使用驼峰命名法
- 名称要具有描述性
- 避免缩写

### 2. 组织结构
- 按页面或功能模块分组
- 相关文本放在一起
- 保持层级清晰

### 3. 文本内容
- 使用简洁明了的中文
- 保持语言风格一致
- 考虑用户体验

### 4. 维护更新
- 及时更新过时的文本
- 保持文本内容的准确性
- 定期检查未使用的文本

## 扩展指南

### 添加新的文本分类

1. 在 `texts` 对象中添加新的分类
2. 定义相应的TypeScript类型
3. 在组件中使用新的文本

```typescript
// 1. 添加新分类
export const texts = {
  // ... 现有内容
  newFeature: {
    title: '新功能',
    description: '这是一个新功能的描述'
  }
}

// 2. 更新类型定义
export type NewFeatureTextKeys = keyof typeof texts.newFeature;

// 3. 在组件中使用
<h1>{texts.newFeature.title}</h1>
```

### 支持多语言

未来可以扩展为支持多语言的国际化系统：

```typescript
// 可能的扩展结构
export const texts = {
  zh: { /* 中文文本 */ },
  en: { /* 英文文本 */ }
}
```

## 注意事项

1. **性能考虑**: 文本对象在应用启动时加载，不会影响运行时性能
2. **类型安全**: 始终使用TypeScript类型检查，避免运行时错误
3. **一致性**: 保持文本风格和用词的一致性
4. **可维护性**: 定期清理未使用的文本，保持文件整洁

---

通过这个统一的文本管理系统，我们可以更好地维护应用的中文内容，提供一致的用户体验。
