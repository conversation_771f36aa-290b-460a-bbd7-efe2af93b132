import type { 
  ApiResponse, 
  PaginatedResponse, 
  ApiError,
  KnowledgeBase,
  CreateKnowledgeBaseRequest,
  UpdateKnowledgeBaseRequest,
  Document,
  SearchRequest,
  SearchResponse
} from '@/types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001/api/v1';

class ApiClient {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // No authentication required

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData: ApiError = await response.json();
        throw new Error(errorData.error.message || 'API request failed');
      }

      return await response.json();
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Knowledge Base API
  async getKnowledgeBases(params?: {
    page?: number;
    size?: number;
    search?: string;
    is_public?: boolean;
    useLangChain?: boolean;
  }): Promise<PaginatedResponse<KnowledgeBase>> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && key !== 'useLangChain') {
          if (Array.isArray(value)) {
            // 处理数组参数 (如 tags)
            value.forEach(v => searchParams.append(key, v.toString()));
          } else {
            searchParams.append(key, value.toString());
          }
        }
      });
    }

    const query = searchParams.toString();
    // Use LangChain API if specified
    const endpoint = params?.useLangChain ? '/langchain-knowledge-bases' : '/knowledge-bases';
    return this.request(`${endpoint}${query ? `?${query}` : ''}`);
  }

  async getKnowledgeBase(id: string, params?: { useLangChain?: boolean }): Promise<KnowledgeBase> {
    const endpoint = params?.useLangChain ? '/langchain-knowledge-bases' : '/knowledge-bases';
    return this.request(`${endpoint}/${id}`);
  }

  async createKnowledgeBase(data: CreateKnowledgeBaseRequest & { useLangChain?: boolean }): Promise<KnowledgeBase> {
    const endpoint = data.useLangChain ? '/langchain-knowledge-bases/' : '/knowledge-bases/';
    const { useLangChain, ...requestData } = data;

    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(requestData),
    });
  }

  async updateKnowledgeBase(
    id: string,
    data: UpdateKnowledgeBaseRequest
  ): Promise<KnowledgeBase> {
    return this.request(`/knowledge-bases/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteKnowledgeBase(id: string): Promise<void> {
    return this.request(`/knowledge-bases/${id}`, {
      method: 'DELETE',
    });
  }

  // Document API
  async getDocuments(
    knowledgeBaseId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      status?: string;
      type?: string;
      useLangChain?: boolean;
    }
  ): Promise<PaginatedResponse<Document>> {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && key !== 'useLangChain') {
          searchParams.append(key, value.toString());
        }
      });
    }

    const query = searchParams.toString();
    const endpoint = params?.useLangChain ? '/langchain-knowledge-bases' : '/knowledge-bases';
    return this.request(
      `${endpoint}/${knowledgeBaseId}/documents${query ? `?${query}` : ''}`
    );
  }

  async uploadDocument(
    knowledgeBaseId: string,
    file: File,
    title?: string,
    description?: string
  ): Promise<any> {
    const formData = new FormData();

    formData.append('file', file);

    if (title) {
      formData.append('title', title);
    }

    if (description) {
      formData.append('description', description);
    }

    // Use LangChain API endpoint
    return this.request(`/langchain-knowledge-bases/${knowledgeBaseId}/documents/upload`, {
      method: 'POST',
      headers: {}, // Remove Content-Type to let browser set it for FormData
      body: formData,
    });
  }

  async uploadDocuments(
    knowledgeBaseId: string,
    files: File[],
    metadata?: any
  ): Promise<ApiResponse<{ documents: Document[] }>> {
    const formData = new FormData();

    files.forEach((file) => {
      formData.append('files', file);
    });

    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    return this.request(`/knowledge-bases/${knowledgeBaseId}/documents`, {
      method: 'POST',
      headers: {}, // Remove Content-Type to let browser set it for FormData
      body: formData,
    });
  }

  async deleteDocument(id: string): Promise<ApiResponse<void>> {
    return this.request(`/documents/${id}`, {
      method: 'DELETE',
    });
  }

  // Search API
  async search(
    knowledgeBaseId: string,
    request: SearchRequest
  ): Promise<ApiResponse<SearchResponse>> {
    return this.request(`/knowledge-bases/${knowledgeBaseId}/search`, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  // Auth API
  async login(email: string, password: string): Promise<ApiResponse<{
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
  }>> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async logout(): Promise<void> {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
  }

  async refreshToken(): Promise<ApiResponse<{
    access_token: string;
    expires_in: number;
  }>> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    return this.request('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refresh_token: refreshToken }),
    });
  }
}

export const apiClient = new ApiClient();
export default apiClient;
