// Base types
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// User types
export interface User extends BaseEntity {
  email: string;
  username: string;
  full_name: string;
  avatar_url?: string;
  is_active: boolean;
  is_superuser: boolean;
  email_verified: boolean;
  last_login_at?: string;
}

// Knowledge Base types
export interface KnowledgeBase extends BaseEntity {
  name: string;
  description?: string;
  document_count: number;
  total_size: number;
  is_public: boolean;
  settings: KnowledgeBaseSettings;
}

export interface KnowledgeBaseSettings {
  embedding_model: string;
  chunk_size: number;
  chunk_overlap: number;
  retrieval_strategy: string;
  access_level: 'public' | 'private' | 'restricted';
  auto_processing: boolean;
}

export interface CreateKnowledgeBaseRequest {
  name: string;
  description?: string;
  is_public?: boolean;
  settings?: Partial<KnowledgeBaseSettings>;
}

export interface UpdateKnowledgeBaseRequest {
  name?: string;
  description?: string;
  is_public?: boolean;
  settings?: Partial<KnowledgeBaseSettings>;
}

// Document types
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'retrying';

export interface Document extends BaseEntity {
  knowledge_base_id: string;
  filename: string;
  original_name: string;
  file_type: string;
  file_size: number;
  storage_path: string;
  processing_status: ProcessingStatus;
  metadata: DocumentMetadata;
  chunk_count: number;
  uploaded_by?: string;
  uploaded_at: string;
  processed_at?: string;
}

export interface DocumentMetadata {
  author?: string;
  title?: string;
  subject?: string;
  keywords?: string[];
  language?: string;
  page_count?: number;
  word_count?: number;
  created_date?: string;
  modified_date?: string;
}

export interface DocumentChunk {
  id: string;
  document_id: string;
  chunk_index: number;
  content: string;
  metadata: ChunkMetadata;
  created_at: string;
}

export interface ChunkMetadata {
  page?: number;
  section?: string;
  [key: string]: any;
}

// Search types
export interface SearchRequest {
  query: string;
  top_k?: number;
  threshold?: number;
  filters?: SearchFilters;
  rerank?: boolean;
  hybrid_search?: boolean;
}

export interface SearchFilters {
  document_types?: string[];
  date_range?: {
    start: string;
    end: string;
  };
  tags?: string[];
}

export interface SearchResult {
  id: string;
  chunk_id: string;
  content: string;
  relevance_score: number;
  rank_position: number;
  document: {
    id: string;
    name: string;
    page?: number;
  };
  metadata: {
    section?: string;
    highlight?: string;
    [key: string]: any;
  };
}

export interface SearchResponse {
  query_id: string;
  results: SearchResult[];
  metadata: SearchMetadata;
}

export interface SearchMetadata {
  total_results: number;
  search_time: number;
  embedding_time: number;
  retrieval_time: number;
  rerank_time?: number;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface PaginationInfo {
  page: number;
  size: number;
  total: number;
  pages: number;
}

export interface ApiError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

// UI State types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface UploadProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
}

// Permission types
export type PermissionLevel = 'owner' | 'editor' | 'viewer' | 'guest';

export interface Permission {
  user_id: string;
  knowledge_base_id: string;
  permission_level: PermissionLevel;
  granted_at: string;
  granted_by?: string;
}
