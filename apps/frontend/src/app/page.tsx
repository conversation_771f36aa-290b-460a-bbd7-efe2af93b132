import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { texts } from '@/lib/texts';

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">教</span>
              </div>
              <h1 className="text-xl font-semibold">{texts.common.appName}</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline">{texts.common.login}</Button>
              <Button>{texts.common.getStarted}</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16">
        <div className="text-center space-y-6 mb-16">
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
            <span className="text-primary">{texts.home.heroTitleHighlight}</span>
          </h1>
          <br/>
          <div className="flex items-center justify-center space-x-4">
            <Link href="/knowledge-bases">
              <Button size="lg">
                {texts.common.startBuilding}
              </Button>
            </Link>
            <Button variant="outline" size="lg">
              {texts.common.viewDocs}
            </Button>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-6 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>📚</span>
                <span>{texts.home.features.knowledgeBase.title}</span>
              </CardTitle>
              <CardDescription>
                {texts.home.features.knowledgeBase.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-muted-foreground space-y-2">
                {texts.home.features.knowledgeBase.items.map((item, index) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>📄</span>
                <span>{texts.home.features.documentProcessing.title}</span>
              </CardTitle>
              <CardDescription>
                {texts.home.features.documentProcessing.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-muted-foreground space-y-2">
                {texts.home.features.documentProcessing.items.map((item, index) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <span>🔍</span>
                <span>{texts.home.features.retrievalTesting.title}</span>
              </CardTitle>
              <CardDescription>
                {texts.home.features.retrievalTesting.description}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="text-sm text-muted-foreground space-y-2">
                {texts.home.features.retrievalTesting.items.map((item, index) => (
                  <li key={index}>• {item}</li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t mt-1">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-sm text-muted-foreground">
            <p>{texts.home.footer.copyright}</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
