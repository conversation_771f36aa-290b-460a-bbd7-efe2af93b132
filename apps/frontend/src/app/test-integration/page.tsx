"use client";

import { useState } from 'react';

export default function TestIntegrationPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testAPI = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8001/api/v1/knowledge-bases/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('API 测试失败:', error);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const createKB = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:8001/api/v1/knowledge-bases/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: `测试知识库 ${new Date().toLocaleTimeString()}`,
          description: '前端集成测试创建的知识库',
          is_public: true,
          settings: {
            embedding_model: 'embedding-3',
            chunk_size: 1000,
            chunk_overlap: 200,
            retrieval_strategy: 'similarity',
            access_level: 'public',
            auto_processing: true
          }
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      setResult(data);
    } catch (error) {
      console.error('创建知识库失败:', error);
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">前后端集成测试</h1>
      
      <div className="space-y-4 mb-8">
        <button
          onClick={testAPI}
          disabled={loading}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
        >
          {loading ? '测试中...' : '测试获取知识库列表'}
        </button>
        
        <button
          onClick={createKB}
          disabled={loading}
          className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50 ml-4"
        >
          {loading ? '创建中...' : '测试创建知识库'}
        </button>
      </div>
      
      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-4">测试结果:</h2>
          <pre className="whitespace-pre-wrap text-sm">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
