"use client";

import { useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Loader2 } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { texts } from "@/lib/texts";
import apiClient from "@/lib/api";
import DocumentUpload, { DocumentUploadRef } from "@/components/features/DocumentUpload";
import LoadingOverlay from "@/components/ui/loading-overlay";

// 表单验证模式
const formSchema = z.object({
  name: z.string().min(1, {
    message: texts.knowledgeBases.create.validation.nameRequired,
  }).max(255, {
    message: texts.knowledgeBases.create.validation.nameMaxLength,
  }),
  description: z.string().optional(),
  is_public: z.boolean().default(false),
  settings: z.object({
    embedding_model: z.string().default("embedding-3"),
    chunk_size: z.number().int().min(100).max(4000).default(1000),
    chunk_overlap: z.number().int().min(0).max(1000).default(200),
    retrieval_strategy: z.string().default("similarity"),
    access_level: z.string().default("private"),
    auto_processing: z.boolean().default(true),
  }).optional(),
});

export default function CreateKnowledgeBasePage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showLoadingOverlay, setShowLoadingOverlay] = useState(false);
  const documentUploadRef = useRef<DocumentUploadRef>(null);

  // 初始化表单
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      is_public: false,
      settings: {
        embedding_model: "embedding-3",
        chunk_size: 1000,
        chunk_overlap: 200,
        retrieval_strategy: "similarity",
        access_level: "private",
        auto_processing: true,
      },
    },
  });

  // 处理表单提交
  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      setIsSubmitting(true);
      setShowLoadingOverlay(true);

      // 1. 创建知识库
      const response = await apiClient.createKnowledgeBase({ ...values, useLangChain: true });
      const knowledgeBaseId = response.id;

      // 2. 检查是否有文档需要上传
      const uploadFiles = documentUploadRef.current?.getFiles() || [];

      if (uploadFiles.length > 0) {
        // 上传所有文档
        let successCount = 0;
        let failCount = 0;

        for (const uploadFile of uploadFiles) {
          try {
            await apiClient.uploadDocument(
              knowledgeBaseId,
              uploadFile.file,
              uploadFile.title,
              uploadFile.description
            );
            successCount++;
          } catch (error) {
            console.error(`Failed to upload ${uploadFile.file.name}:`, error);
            failCount++;
          }
        }

        // 显示上传结果
        if (failCount === 0) {
          toast({
            title: "创建完成",
            description: `知识库创建成功，已上传 ${successCount} 个文档`,
          });
        } else {
          toast({
            title: "部分成功",
            description: `知识库创建成功，${successCount} 个文档上传成功，${failCount} 个失败`,
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "创建完成",
          description: "知识库创建成功",
        });
      }

    } catch (error) {
      console.error("Failed to create knowledge base:", error);
      setShowLoadingOverlay(false);
      toast({
        title: texts.knowledgeBases.create.error.title,
        description: texts.knowledgeBases.create.error.description,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理加载完成
  const handleLoadingComplete = () => {
    setShowLoadingOverlay(false);

    // 跳转到知识库列表
    setTimeout(() => {
      router.push("/knowledge-bases");
      router.refresh();
    }, 500);
  };



  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {texts.knowledgeBases.create.title}
          </h1>
          <p className="text-muted-foreground mt-2">
            {texts.knowledgeBases.create.description}
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => router.push("/knowledge-bases")}
        >
          {texts.common.cancel}
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{texts.knowledgeBases.create.formTitle}</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{texts.knowledgeBases.create.form.name}</FormLabel>
                    <FormControl>
                      <Input placeholder={texts.knowledgeBases.create.form.namePlaceholder} {...field} />
                    </FormControl>
                    <FormDescription>
                      {texts.knowledgeBases.create.form.nameDescription}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{texts.knowledgeBases.create.form.description}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={texts.knowledgeBases.create.form.descriptionPlaceholder}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {texts.knowledgeBases.create.form.descriptionDescription}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />



              <FormField
                control={form.control}
                name="is_public"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        {texts.knowledgeBases.create.form.isPublic}
                      </FormLabel>
                      <FormDescription>
                        {texts.knowledgeBases.create.form.isPublicDescription}
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <Separator />

              {/* 文档上传部分 */}
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">
                    {texts.knowledgeBases.create.documents.title}
                    <span className="text-sm text-muted-foreground ml-2">
                      {texts.knowledgeBases.create.documents.optional}
                    </span>
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    {texts.knowledgeBases.create.documents.description}
                  </p>
                </div>

                <DocumentUpload
                  ref={documentUploadRef}
                  knowledgeBaseId={null} // 不需要预先创建的ID
                  disabled={isSubmitting}
                  maxFiles={5}
                />
              </div>

              <Separator />

              <div className="flex justify-end">
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {isSubmitting ? '创建中...' : texts.knowledgeBases.create.form.submit}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* 加载覆盖层 */}
      <LoadingOverlay
        isVisible={showLoadingOverlay}
        title="创建知识库中"
        onComplete={handleLoadingComplete}
      />
    </div>
  );
}
