"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { ArrowLeft, FileText, Upload, Settings, Search, MoreVertical, Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { texts } from "@/lib/texts";
import apiClient from "@/lib/api";
import DocumentUpload from "@/components/features/DocumentUpload";
import LoadingOverlay from "@/components/ui/loading-overlay";

interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  document_count: number;
  total_size: number;
  is_public: boolean;
  created_at: string;
  updated_at: string;
}

interface Document {
  id: string;
  title: string;
  filename: string;
  file_size: number;
  status: string;
  created_at: string;
  updated_at: string;
}

export default function KnowledgeBaseDetailPage() {
  const params = useParams();
  const router = useRouter();
  const knowledgeBaseId = params.id as string;

  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBase | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [showUploadOverlay, setShowUploadOverlay] = useState(false);

  // 获取知识库详情
  useEffect(() => {
    const fetchKnowledgeBase = async () => {
      try {
        setLoading(true);
        const response = await apiClient.getKnowledgeBase(knowledgeBaseId, { useLangChain: true });
        setKnowledgeBase(response);
      } catch (error) {
        console.error("Failed to fetch knowledge base:", error);
        toast({
          title: "获取失败",
          description: "无法获取知识库信息",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (knowledgeBaseId) {
      fetchKnowledgeBase();
    }
  }, [knowledgeBaseId]);

  // 获取文档列表
  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setDocumentsLoading(true);
        const response = await apiClient.getDocuments(knowledgeBaseId, { useLangChain: true });
        setDocuments(response.items || []);
      } catch (error) {
        console.error("Failed to fetch documents:", error);
        // 如果API失败，设置空数组
        setDocuments([]);
      } finally {
        setDocumentsLoading(false);
      }
    };

    if (knowledgeBaseId) {
      fetchDocuments();
    }
  }, [knowledgeBaseId]);

  // 处理文档上传完成
  const handleDocumentUploadComplete = async () => {
    toast({
      title: "上传完成",
      description: "文档已成功上传并处理",
    });

    // 重新获取文档列表和知识库信息
    try {
      console.log("Refreshing data after upload...");

      const [kbResponse, docsResponse] = await Promise.all([
        apiClient.getKnowledgeBase(knowledgeBaseId, { useLangChain: true }),
        apiClient.getDocuments(knowledgeBaseId, { useLangChain: true })
      ]);

      console.log("Updated knowledge base:", kbResponse);
      console.log("Updated documents:", docsResponse);

      setKnowledgeBase(kbResponse);
      setDocuments(docsResponse.items || []);
    } catch (error) {
      console.error("Failed to refresh data:", error);
      toast({
        title: "刷新失败",
        description: "无法获取最新数据，请刷新页面",
        variant: "destructive",
      });
    }
  };

  // 处理上传覆盖层完成
  const handleUploadOverlayComplete = () => {
    setShowUploadOverlay(false);
    handleDocumentUploadComplete();
  };

  // 格式化文件大小
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  if (!knowledgeBase) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">知识库不存在</h2>
          <p className="text-muted-foreground mb-4">请检查链接是否正确</p>
          <Button onClick={() => router.push('/knowledge-bases')}>
            返回知识库列表
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => router.push('/knowledge-bases')}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-xl font-semibold">{knowledgeBase.name}</h1>
                <p className="text-sm text-muted-foreground">知识库详情</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Search className="h-4 w-4 mr-2" />
                搜索
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                设置
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Knowledge Base Info */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-2xl mb-2">{knowledgeBase.name}</CardTitle>
                <CardDescription className="text-base">
                  {knowledgeBase.description}
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={knowledgeBase.is_public ? "default" : "secondary"}>
                  {knowledgeBase.is_public ? "公开" : "私有"}
                </Badge>
                <Button variant="ghost" size="icon">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-primary">{knowledgeBase.document_count}</div>
                <div className="text-sm text-muted-foreground">文档数量</div>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-primary">{formatBytes(knowledgeBase.total_size)}</div>
                <div className="text-sm text-muted-foreground">总大小</div>
              </div>
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-2xl font-bold text-primary">{formatDate(knowledgeBase.updated_at).split(' ')[0]}</div>
                <div className="text-sm text-muted-foreground">最后更新</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs defaultValue="documents" className="space-y-6">
          <TabsList>
            <TabsTrigger value="documents">文档管理</TabsTrigger>
            <TabsTrigger value="search">搜索测试</TabsTrigger>
            <TabsTrigger value="analytics">使用分析</TabsTrigger>
          </TabsList>

          <TabsContent value="documents" className="space-y-6">
            {/* Upload Section */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center space-x-2">
                      <Upload className="h-5 w-5" />
                      <span>添加文档</span>
                    </CardTitle>
                    <CardDescription>
                      上传新文档到此知识库
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <DocumentUpload
                  knowledgeBaseId={knowledgeBaseId}
                  onUploadStart={() => setShowUploadOverlay(true)}
                  onUploadComplete={handleUploadOverlayComplete}
                  maxFiles={10}
                />
              </CardContent>
            </Card>

            {/* Documents List */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>文档列表</span>
                  <Badge variant="outline">{documents.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {documentsLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm text-muted-foreground">加载文档列表...</p>
                  </div>
                ) : documents.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">暂无文档</h3>
                    <p className="text-muted-foreground mb-4">
                      开始上传文档来构建您的知识库
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {documents.map((doc) => (
                      <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <h4 className="font-medium">{doc.title || doc.filename}</h4>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                              <span>{formatBytes(doc.file_size)}</span>
                              <span>•</span>
                              <span>{formatDate(doc.created_at)}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant={doc.status === 'completed' ? 'default' : 'secondary'}>
                            {doc.status === 'completed' ? '已完成' : '处理中'}
                          </Badge>
                          <Button variant="ghost" size="icon">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="search">
            <Card>
              <CardHeader>
                <CardTitle>搜索测试</CardTitle>
                <CardDescription>
                  测试知识库的搜索和检索功能
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">搜索功能即将推出...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>使用分析</CardTitle>
                <CardDescription>
                  查看知识库的使用统计和分析数据
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">分析功能即将推出...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>

      {/* Upload Loading Overlay */}
      <LoadingOverlay
        isVisible={showUploadOverlay}
        title="上传文档中"
        steps={[
          {
            id: "uploading",
            title: "上传文档",
            description: "正在上传您的文档文件...",
            icon: <Upload className="h-6 w-6" />,
            duration: 3000,
          },
          {
            id: "processing",
            title: "处理文档",
            description: "正在分析和向量化文档内容...",
            icon: <FileText className="h-6 w-6" />,
            duration: 4000,
          },
          {
            id: "finalizing",
            title: "完成处理",
            description: "正在完成最后的配置...",
            icon: <Plus className="h-6 w-6" />,
            duration: 1000,
          },
        ]}
        onComplete={handleUploadOverlayComplete}
      />
    </div>
  );
}
