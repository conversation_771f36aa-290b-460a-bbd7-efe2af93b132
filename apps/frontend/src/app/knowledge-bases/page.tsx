'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { formatRelativeTime } from '@/lib/utils';
import { Plus, Search, Filter, MoreVertical, FileText, Users, Calendar } from 'lucide-react';
import { texts, formatBytesZh } from '@/lib/texts';
import Link from 'next/link';
import apiClient from '@/lib/api';

// Mock data for demonstration
const mockKnowledgeBases = [
  {
    id: '1',
    name: '计算机科学课程',
    description: '计算机科学课程材料、教科书和研究论文的综合集合。',

    document_count: 45,
    total_size: 125000000, // 125MB
    is_public: false,
    created_at: '2024-12-01T10:00:00Z',
    updated_at: '2025-01-10T15:30:00Z',
    settings: {
      embedding_model: "embedding-3",
      chunk_size: 1000,
      chunk_overlap: 200,
      retrieval_strategy: "similarity",
      access_level: "private",
      auto_processing: true
    }
  },
  {
    id: '2',
    name: '机器学习研究',
    description: '机器学习和人工智能领域的最新研究论文和数据集。',

    document_count: 78,
    total_size: 340000000, // 340MB
    is_public: true,
    created_at: '2024-11-15T09:00:00Z',
    updated_at: '2025-01-12T11:20:00Z',
    settings: {
      embedding_model: "embedding-3",
      chunk_size: 1000,
      chunk_overlap: 200,
      retrieval_strategy: "similarity",
      access_level: "public",
      auto_processing: true
    }
  },
  {
    id: '3',
    name: '学生手册',
    description: '大学政策、程序和学生资源。',

    document_count: 12,
    total_size: 25000000, // 25MB
    is_public: true,
    created_at: '2024-10-20T14:00:00Z',
    updated_at: '2024-12-15T16:45:00Z',
    settings: {
      embedding_model: "embedding-3",
      chunk_size: 1000,
      chunk_overlap: 200,
      retrieval_strategy: "similarity",
      access_level: "public",
      auto_processing: true
    }
  }
];

export default function KnowledgeBasesPage() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [knowledgeBases, setKnowledgeBases] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // 获取知识库列表
  useEffect(() => {
    const fetchKnowledgeBases = async () => {
      try {
        setLoading(true);
        const response = await apiClient.getKnowledgeBases({ useLangChain: true });
        console.log('API Response:', response); // 调试日志

        // 只使用API数据，不混合mock数据
        const apiKnowledgeBases = response.items || [];
        setKnowledgeBases(apiKnowledgeBases);
      } catch (error) {
        console.error('Failed to fetch knowledge bases:', error);
        // 如果 API 失败，只显示 mock 数据
        setKnowledgeBases(mockKnowledgeBases);
      } finally {
        setLoading(false);
      }
    };

    fetchKnowledgeBases();
  }, []);

  const filteredKnowledgeBases = knowledgeBases.filter(kb => {
    const matchesSearch = kb.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (kb.description && kb.description.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesFilter = selectedFilter === 'all' ||
                         (selectedFilter === 'public' && kb.is_public) ||
                         (selectedFilter === 'private' && !kb.is_public);

    return matchesSearch && matchesFilter;
  });

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">教</span>
              </div>
              <h1 className="text-xl font-semibold">{texts.common.appShortName}</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline">{texts.common.profile}</Button>
              <Button variant="outline">{texts.common.settings}</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{texts.knowledgeBases.title}</h1>
            <p className="text-muted-foreground mt-2">
              {texts.knowledgeBases.description}
            </p>
          </div>
          <Link href="/knowledge-bases/create">
            <Button className="flex items-center space-x-2">
              <Plus className="h-4 w-4" />
              <span>{texts.knowledgeBases.createButton}</span>
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder={texts.knowledgeBases.searchPlaceholder}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={selectedFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFilter('all')}
            >
              {texts.knowledgeBases.filters.all}
            </Button>
            <Button
              variant={selectedFilter === 'public' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFilter('public')}
            >
              {texts.knowledgeBases.filters.public}
            </Button>
            <Button
              variant={selectedFilter === 'private' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedFilter('private')}
            >
              {texts.knowledgeBases.filters.private}
            </Button>
          </div>
        </div>

        {/* Knowledge Bases Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredKnowledgeBases.map((kb) => (
            <Card
              key={kb.id}
              className="hover:shadow-lg transition-shadow cursor-pointer"
              onClick={() => router.push(`/knowledge-bases/${kb.id}`)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg mb-2">{kb.name}</CardTitle>
                    <CardDescription className="text-sm line-clamp-2">
                      {kb.description}
                    </CardDescription>
                  </div>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </div>

              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <FileText className="h-4 w-4" />
                      <span>{kb.document_count} {texts.knowledgeBases.card.documents}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>{formatBytesZh(kb.total_size)}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center space-x-1">
                      <Users className="h-4 w-4" />
                      <span>{kb.owner?.name || '系统'}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      {kb.is_public ? (
                        <Badge variant="outline" className="text-xs">{texts.knowledgeBases.card.public}</Badge>
                      ) : (
                        <Badge variant="secondary" className="text-xs">{texts.knowledgeBases.card.private}</Badge>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center text-xs text-muted-foreground">
                    <Calendar className="h-3 w-3 mr-1" />
                    <span>{texts.knowledgeBases.card.updated} {formatRelativeTime(kb.updated_at)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {filteredKnowledgeBases.length === 0 && (
          <div className="text-center py-12">
            <div className="mx-auto h-24 w-24 rounded-full bg-muted flex items-center justify-center mb-4">
              <FileText className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold mb-2">{texts.knowledgeBases.empty.title}</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery ? texts.knowledgeBases.empty.searchDescription : texts.knowledgeBases.empty.createDescription}
            </p>
            <Link href="/knowledge-bases/create">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                {texts.knowledgeBases.empty.createButton}
              </Button>
            </Link>
          </div>
        )}
      </main>
    </div>
  );
}
