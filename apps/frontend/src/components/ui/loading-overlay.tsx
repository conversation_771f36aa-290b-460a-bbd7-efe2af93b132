"use client";

import { useState, useEffect } from "react";
import { Upload, CheckCircle, FileText, Database, Sparkles } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface LoadingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  duration: number; // 持续时间（毫秒）
}

interface LoadingOverlayProps {
  isVisible: boolean;
  title?: string;
  steps?: LoadingStep[];
  onComplete?: () => void;
}

const defaultSteps: LoadingStep[] = [
  {
    id: "creating",
    title: "创建知识库",
    description: "正在初始化知识库结构...",
    icon: <Database className="h-6 w-6" />,
    duration: 2000,
  },
  {
    id: "uploading",
    title: "上传文档",
    description: "正在上传您的文档文件...",
    icon: <Upload className="h-6 w-6" />,
    duration: 3000,
  },
  {
    id: "processing",
    title: "处理文档",
    description: "正在分析和向量化文档内容...",
    icon: <FileText className="h-6 w-6" />,
    duration: 4000,
  },
  {
    id: "finalizing",
    title: "完成设置",
    description: "正在完成最后的配置...",
    icon: <Sparkles className="h-6 w-6" />,
    duration: 1000,
  },
];

export default function LoadingOverlay({
  isVisible,
  title = "创建知识库中",
  steps = defaultSteps,
  onComplete,
}: LoadingOverlayProps) {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [progress, setProgress] = useState(0);
  const [stepProgress, setStepProgress] = useState(0);

  useEffect(() => {
    if (!isVisible) {
      setCurrentStepIndex(0);
      setProgress(0);
      setStepProgress(0);
      return;
    }

    const currentStep = steps[currentStepIndex];
    if (!currentStep) return;

    // 步骤内进度动画
    const stepInterval = setInterval(() => {
      setStepProgress((prev) => {
        if (prev >= 100) {
          return 100;
        }
        return prev + (100 / (currentStep.duration / 100));
      });
    }, 100);

    // 步骤切换
    const stepTimeout = setTimeout(() => {
      setStepProgress(0);
      
      if (currentStepIndex < steps.length - 1) {
        setCurrentStepIndex((prev) => prev + 1);
        setProgress(((currentStepIndex + 1) / steps.length) * 100);
      } else {
        // 所有步骤完成
        setProgress(100);
        setTimeout(() => {
          onComplete?.();
        }, 500);
      }
    }, currentStep.duration);

    return () => {
      clearInterval(stepInterval);
      clearTimeout(stepTimeout);
    };
  }, [isVisible, currentStepIndex, steps, onComplete]);

  if (!isVisible) return null;

  const currentStep = steps[currentStepIndex];

  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
      <Card className="w-full max-w-md mx-4">
        <CardContent className="p-8">
          <div className="text-center space-y-6">
            {/* 主标题 */}
            <div>
              <h2 className="text-2xl font-bold mb-2">{title}</h2>
              <p className="text-muted-foreground">请稍候，正在为您处理...</p>
            </div>

            {/* 动画图标区域 */}
            <div className="relative">
              <div className="w-24 h-24 mx-auto mb-4 relative">
                {/* 背景圆圈 */}
                <div className="absolute inset-0 rounded-full border-4 border-muted"></div>
                
                {/* 进度圆圈 */}
                <div 
                  className="absolute inset-0 rounded-full border-4 border-primary border-t-transparent animate-spin"
                  style={{
                    animationDuration: "2s",
                  }}
                ></div>
                
                {/* 中心图标 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-primary animate-pulse">
                    {currentStep?.icon}
                  </div>
                </div>
              </div>

              {/* 上传动画效果 */}
              <div className="absolute -top-2 -right-2 w-8 h-8">
                <div className="w-full h-full rounded-full bg-primary/20 animate-ping"></div>
                <div className="absolute inset-0 w-full h-full rounded-full bg-primary/40 animate-ping" style={{ animationDelay: '0.75s' }}></div>
              </div>
            </div>

            {/* 当前步骤信息 */}
            {currentStep && (
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold text-lg">{currentStep.title}</h3>
                  <p className="text-sm text-muted-foreground">{currentStep.description}</p>
                </div>

                {/* 步骤进度条 */}
                <div className="space-y-2">
                  <Progress value={stepProgress} className="h-2" />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>步骤 {currentStepIndex + 1} / {steps.length}</span>
                    <span>{Math.round(stepProgress)}%</span>
                  </div>
                </div>
              </div>
            )}

            {/* 总体进度 */}
            <div className="space-y-2">
              <Progress value={progress} className="h-1" />
              <p className="text-xs text-muted-foreground">
                总进度: {Math.round(progress)}%
              </p>
            </div>

            {/* 步骤列表 */}
            <div className="space-y-2">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={`flex items-center space-x-3 p-2 rounded-lg transition-colors ${
                    index < currentStepIndex
                      ? "bg-green-50 text-green-700"
                      : index === currentStepIndex
                      ? "bg-blue-50 text-blue-700"
                      : "text-muted-foreground"
                  }`}
                >
                  <div className="flex-shrink-0">
                    {index < currentStepIndex ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : index === currentStepIndex ? (
                      <div className="h-4 w-4 rounded-full border-2 border-blue-600 border-t-transparent animate-spin"></div>
                    ) : (
                      <div className="h-4 w-4 rounded-full border-2 border-muted-foreground/30"></div>
                    )}
                  </div>
                  <span className="text-sm font-medium">{step.title}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
