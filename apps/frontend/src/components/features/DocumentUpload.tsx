"use client";

import { useState, useCallback, useImperative<PERSON><PERSON><PERSON>, forwardRef } from "react";
import { useDropzone } from "react-dropzone";
import { Upload, File, X, CheckCircle, AlertCircle, Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/components/ui/use-toast";
import { texts } from "@/lib/texts";
import apiClient from "@/lib/api";

interface UploadFile {
  file: File;
  id: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  title?: string;
  description?: string;
}

interface DocumentUploadProps {
  knowledgeBaseId?: string;
  onUploadComplete?: (files: UploadFile[]) => void;
  onUploadStart?: (files: UploadFile[]) => void;
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
}

export interface DocumentUploadRef {
  hasFiles: () => boolean;
  getFiles: () => UploadFile[];
  clearFiles: () => void;
}

const DocumentUpload = forwardRef<DocumentUploadRef, DocumentUploadProps>(({
  knowledgeBaseId,
  onUploadComplete,
  onUploadStart,
  maxFiles = 10,
  disabled = false,
  className = "",
}, ref) => {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    hasFiles: () => uploadFiles.length > 0,
    getFiles: () => uploadFiles,
    clearFiles: () => setUploadFiles([]),
  }));

  // 支持的文件类型
  const acceptedFileTypes = {
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'application/msword': ['.doc'],
    'text/plain': ['.txt'],
    'text/markdown': ['.md'],
    'text/html': ['.html'],
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (disabled) return;

    const newFiles: UploadFile[] = acceptedFiles.map((file) => ({
      file,
      id: Math.random().toString(36).substr(2, 9),
      status: 'pending',
      progress: 0,
      title: file.name.split('.').slice(0, -1).join('.'), // 文件名作为默认标题
    }));

    setUploadFiles(prev => [...prev, ...newFiles].slice(0, maxFiles));
  }, [disabled, maxFiles]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes,
    maxFiles,
    disabled: disabled || isUploading,
    maxSize: 50 * 1024 * 1024, // 50MB
  });

  const removeFile = (fileId: string) => {
    setUploadFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const updateFileTitle = (fileId: string, title: string) => {
    setUploadFiles(prev => prev.map(f => 
      f.id === fileId ? { ...f, title } : f
    ));
  };

  const updateFileDescription = (fileId: string, description: string) => {
    setUploadFiles(prev => prev.map(f => 
      f.id === fileId ? { ...f, description } : f
    ));
  };

  const uploadSingleFile = async (uploadFile: UploadFile): Promise<void> => {
    if (!knowledgeBaseId) {
      throw new Error('Knowledge base ID is required');
    }

    // 更新状态为上传中
    setUploadFiles(prev => prev.map(f => 
      f.id === uploadFile.id 
        ? { ...f, status: 'uploading', progress: 0 }
        : f
    ));

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadFiles(prev => prev.map(f => 
          f.id === uploadFile.id && f.progress < 90
            ? { ...f, progress: f.progress + 10 }
            : f
        ));
      }, 200);

      // 调用API上传文档
      const response = await apiClient.uploadDocument(
        knowledgeBaseId,
        uploadFile.file,
        uploadFile.title,
        uploadFile.description
      );

      clearInterval(progressInterval);

      // 更新状态为处理中
      setUploadFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { ...f, status: 'processing', progress: 100 }
          : f
      ));

      // 模拟处理时间
      setTimeout(() => {
        setUploadFiles(prev => prev.map(f =>
          f.id === uploadFile.id
            ? { ...f, status: 'completed', progress: 100 }
            : f
        ));
      }, 1000);

    } catch (error) {
      console.error('Upload failed:', error);
      setUploadFiles(prev => prev.map(f => 
        f.id === uploadFile.id 
          ? { 
              ...f, 
              status: 'error', 
              progress: 0,
              error: error instanceof Error ? error.message : '上传失败'
            }
          : f
      ));
    }
  };

  const startUpload = async () => {
    if (!knowledgeBaseId) {
      // 如果没有知识库ID，说明是在创建阶段，不需要立即上传
      // 文件会在表单提交时一起处理
      return;
    }

    const pendingFiles = uploadFiles.filter(f => f.status === 'pending');
    if (pendingFiles.length === 0) return;

    setIsUploading(true);
    onUploadStart?.(uploadFiles);

    try {
      // 并发上传所有文件
      await Promise.all(pendingFiles.map(uploadSingleFile));

      toast({
        title: texts.success.documentUploaded,
        description: `成功上传 ${pendingFiles.length} 个文档`,
      });

      // 等待一下确保所有文件都处理完成
      setTimeout(() => {
        onUploadComplete?.(uploadFiles);
      }, 1000);
    } catch (error) {
      console.error('Batch upload failed:', error);
      toast({
        title: "上传失败",
        description: "部分文档上传失败，请重试",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const getStatusIcon = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return <File className="h-4 w-4 text-muted-foreground" />;
      case 'uploading':
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <File className="h-4 w-4" />;
    }
  };

  const getStatusText = (status: UploadFile['status']) => {
    switch (status) {
      case 'pending':
        return '等待上传';
      case 'uploading':
        return '上传中...';
      case 'processing':
        return '处理中...';
      case 'completed':
        return '已完成';
      case 'error':
        return '上传失败';
      default:
        return '';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 拖拽上传区域 */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
              ${isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25'}
              ${disabled || isUploading ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary hover:bg-primary/5'}
            `}
          >
            <input {...getInputProps()} />
            <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {isDragActive ? '释放文件以上传' : texts.documents.upload.dragDrop}
            </h3>
            <p className="text-sm text-muted-foreground mb-2">
              {texts.documents.upload.supportedFormats}
            </p>
            <p className="text-xs text-muted-foreground">
              {texts.documents.upload.maxSize}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* 文件列表 */}
      {uploadFiles.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">
                  {knowledgeBaseId ? '待上传文件' : '已选择文件'} ({uploadFiles.length})
                </h4>
                {knowledgeBaseId && uploadFiles.some(f => f.status === 'pending') && (
                  <Button
                    onClick={startUpload}
                    disabled={isUploading}
                    size="sm"
                  >
                    {isUploading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    开始上传
                  </Button>
                )}
              </div>

              {uploadFiles.map((uploadFile) => (
                <div key={uploadFile.id} className="border rounded-lg p-3 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(uploadFile.status)}
                      <span className="font-medium text-sm">{uploadFile.file.name}</span>
                      <span className="text-xs text-muted-foreground">
                        ({(uploadFile.file.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-muted-foreground">
                        {getStatusText(uploadFile.status)}
                      </span>
                      {uploadFile.status === 'pending' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(uploadFile.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* 进度条 */}
                  {(uploadFile.status === 'uploading' || uploadFile.status === 'processing') && (
                    <Progress value={uploadFile.progress} className="h-2" />
                  )}

                  {/* 错误信息 */}
                  {uploadFile.status === 'error' && uploadFile.error && (
                    <p className="text-xs text-red-500">{uploadFile.error}</p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
});

DocumentUpload.displayName = "DocumentUpload";

export default DocumentUpload;
