import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/lib/api';
import type { 
  KnowledgeBase, 
  CreateKnowledgeBaseRequest, 
  UpdateKnowledgeBaseRequest 
} from '@/types';

// Query keys
export const knowledgeBaseKeys = {
  all: ['knowledge-bases'] as const,
  lists: () => [...knowledgeBaseKeys.all, 'list'] as const,
  list: (params?: any) => [...knowledgeBaseKeys.lists(), params] as const,
  details: () => [...knowledgeBaseKeys.all, 'detail'] as const,
  detail: (id: string) => [...knowledgeBaseKeys.details(), id] as const,
};

// Hooks
export function useKnowledgeBases(params?: {
  page?: number;
  limit?: number;
  search?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}) {
  return useQuery({
    queryKey: knowledgeBaseKeys.list(params),
    queryFn: () => apiClient.getKnowledgeBases(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useKnowledgeBase(id: string) {
  return useQuery({
    queryKey: knowledgeBaseKeys.detail(id),
    queryFn: () => apiClient.getKnowledgeBase(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCreateKnowledgeBase() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateKnowledgeBaseRequest) =>
      apiClient.createKnowledgeBase(data),
    onSuccess: () => {
      // Invalidate and refetch knowledge bases list
      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.lists() });
    },
  });
}

export function useUpdateKnowledgeBase() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateKnowledgeBaseRequest }) =>
      apiClient.updateKnowledgeBase(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate specific knowledge base and list
      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.lists() });
    },
  });
}

export function useDeleteKnowledgeBase() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => apiClient.deleteKnowledgeBase(id),
    onSuccess: (_, id) => {
      // Remove from cache and invalidate list
      queryClient.removeQueries({ queryKey: knowledgeBaseKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.lists() });
    },
  });
}
