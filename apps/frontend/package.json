{"name": "education-rag-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.17.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^3.0.0", "framer-motion": "^11.0.0", "lucide-react": "^0.294.0", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.1", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zustand": "^4.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.83.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^14.1.0", "@testing-library/user-event": "^14.5.0", "@types/jest": "^29.5.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "tailwindcss": "^4", "typescript": "^5"}}