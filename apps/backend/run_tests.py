#!/usr/bin/env python3
"""测试运行脚本"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def run_command(cmd: list, cwd: str = None) -> int:
    """运行命令并返回退出码"""
    print(f"运行命令: {' '.join(cmd)}")
    if cwd:
        print(f"工作目录: {cwd}")
    
    result = subprocess.run(cmd, cwd=cwd)
    return result.returncode


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行 Education RAG Backend 测试")
    
    # 测试类型选项
    parser.add_argument(
        "--type", 
        choices=["unit", "integration", "api", "service", "all"],
        default="unit",
        help="要运行的测试类型 (默认: unit)"
    )
    
    # 测试文件选项
    parser.add_argument(
        "--file",
        help="运行特定的测试文件"
    )
    
    # 测试函数选项
    parser.add_argument(
        "--test",
        help="运行特定的测试函数"
    )
    
    # 详细输出
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    # 覆盖率报告
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="生成覆盖率报告"
    )
    
    # 跳过慢速测试
    parser.add_argument(
        "--skip-slow",
        action="store_true",
        help="跳过慢速测试"
    )
    
    # 跳过集成测试
    parser.add_argument(
        "--skip-integration",
        action="store_true",
        help="跳过集成测试"
    )
    
    args = parser.parse_args()
    
    # 设置工作目录
    backend_dir = Path(__file__).parent
    os.chdir(backend_dir)
    
    # 构建 pytest 命令
    cmd = ["python", "-m", "pytest"]
    
    # 添加详细输出
    if args.verbose:
        cmd.append("-v")
    
    # 添加覆盖率
    if args.coverage:
        cmd.extend([
            "--cov=src/education_rag_backend",
            "--cov-report=html",
            "--cov-report=term-missing"
        ])
    
    # 添加标记过滤
    markers = []
    
    if args.skip_slow:
        markers.append("not slow")
    
    if args.skip_integration:
        markers.append("not integration")
    
    # 根据测试类型添加标记
    if args.type != "all":
        markers.append(args.type)
    
    if markers:
        cmd.extend(["-m", " and ".join(markers)])
    
    # 添加特定文件或测试
    if args.file:
        if args.test:
            cmd.append(f"{args.file}::{args.test}")
        else:
            cmd.append(args.file)
    elif args.test:
        cmd.extend(["-k", args.test])
    
    # 设置环境变量
    env = os.environ.copy()
    env["PYTHONPATH"] = str(backend_dir / "src")
    
    # 运行测试
    print("=" * 60)
    print("运行 Education RAG Backend 测试")
    print("=" * 60)
    
    exit_code = subprocess.run(cmd, env=env).returncode
    
    if exit_code == 0:
        print("\n✅ 所有测试通过!")
    else:
        print(f"\n❌ 测试失败 (退出码: {exit_code})")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
