# Database Configuration
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/education_rag
REDIS_URL=redis://localhost:6379/0

# Pinecone Configuration
PINECONE_API_KEY=pcsk_6SSbjz_AW7G1WcndsSwGMPCnBwyuSEA3kpWcFGvAbbTs6MPyQKvi4jGeCunwADVkHoKbzN
PINECONE_ENVIRONMENT=us-east-1
PINECONE_INDEX_NAME=education-rag

# 智谱AI Configuration (Embedding)
ZHIPU_API_KEY=b416ef77699448858ba295b103ded93c.MHsFdHEvoG2MFKjT
ZHIPU_MODEL=embedding-3

# DeepSeek Configuration (LLM)
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_MODEL=deepseek-chat

# Application Configuration
SECRET_KEY=your-secret-key-here-change-in-production
DEBUG=true
ALLOWED_HOSTS=localhost,127.0.0.1

# File Storage Configuration
STORAGE_TYPE=local
STORAGE_PATH=/Users/<USER>/Projects/ai/freelancer/education-rag/data
MAX_FILE_SIZE=104857600  # 100MB

# JWT Configuration
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
JWT_REFRESH_EXPIRE_DAYS=7

# Document Processing Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
EMBEDDING_DIMENSION=2048  # 智谱AI embedding-3 的维度

# Celery Configuration (for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
