"""前端集成测试"""

import asyncio
import pytest
from playwright.async_api import async_playwright, <PERSON>, Browser
import requests
import time


@pytest.mark.integration
@pytest.mark.slow
class TestFrontendIntegration:
    """前端集成测试类"""
    
    @pytest.fixture(scope="class")
    async def browser_context(self):
        """创建浏览器上下文"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)  # 设置为 False 以便观察
            context = await browser.new_context()
            yield context
            await browser.close()
    
    @pytest.fixture
    async def page(self, browser_context):
        """创建页面"""
        page = await browser_context.new_page()
        yield page
        await page.close()
    
    def test_backend_is_running(self):
        """确保后端正在运行"""
        try:
            response = requests.get("http://localhost:8001/api/v1/knowledge-bases/", timeout=5)
            assert response.status_code == 200
        except Exception as e:
            pytest.skip(f"后端服务未运行: {e}")
    
    def test_frontend_is_running(self):
        """确保前端正在运行"""
        try:
            response = requests.get("http://localhost:3001", timeout=5)
            assert response.status_code == 200
        except Exception as e:
            pytest.skip(f"前端服务未运行: {e}")
    
    @pytest.mark.asyncio
    async def test_knowledge_base_list_page(self, page: Page):
        """测试知识库列表页面"""
        # 导航到知识库列表页面
        await page.goto("http://localhost:3001/knowledge-bases")
        
        # 等待页面加载
        await page.wait_for_load_state("networkidle")
        
        # 检查页面标题
        title = await page.title()
        assert "Education RAG" in title or "知识库" in title
        
        # 检查是否有知识库列表
        await page.wait_for_selector('[data-testid="knowledge-base-card"], .grid', timeout=10000)
        
        # 检查是否有创建按钮
        create_button = page.locator('text="创建知识库"').or_(page.locator('text="Create"'))
        await create_button.wait_for(timeout=5000)
        
        print("✅ 知识库列表页面加载成功")
    
    @pytest.mark.asyncio
    async def test_create_knowledge_base_flow(self, page: Page):
        """测试创建知识库的完整流程"""
        # 导航到知识库列表页面
        await page.goto("http://localhost:3001/knowledge-bases")
        await page.wait_for_load_state("networkidle")
        
        # 点击创建知识库按钮
        create_button = page.locator('text="创建知识库"').or_(page.locator('a[href="/knowledge-bases/create"]'))
        await create_button.click()
        
        # 等待创建页面加载
        await page.wait_for_url("**/knowledge-bases/create")
        await page.wait_for_load_state("networkidle")
        
        # 填写表单
        test_name = f"Playwright测试知识库 {int(time.time())}"
        
        # 填写名称
        name_input = page.locator('input[name="name"]').or_(page.locator('input[placeholder*="名称"]'))
        await name_input.fill(test_name)
        
        # 填写描述
        desc_input = page.locator('textarea[name="description"]').or_(page.locator('textarea[placeholder*="描述"]'))
        await desc_input.fill("这是通过 Playwright 自动化测试创建的知识库")
        
        # 添加标签
        tag_input = page.locator('input[placeholder*="标签"]').or_(page.locator('input[name="tag"]'))
        if await tag_input.count() > 0:
            await tag_input.fill("playwright")
            await page.keyboard.press("Enter")
            await tag_input.fill("测试")
            await page.keyboard.press("Enter")
        
        # 提交表单
        submit_button = page.locator('button[type="submit"]').or_(page.locator('text="创建"'))
        await submit_button.click()
        
        # 等待成功消息或跳转
        try:
            # 等待成功提示或跳转到列表页
            await page.wait_for_url("**/knowledge-bases", timeout=10000)
            print("✅ 成功跳转到知识库列表页面")
        except:
            # 如果没有跳转，检查是否有成功消息
            success_message = page.locator('text="成功"').or_(page.locator('text="创建成功"'))
            if await success_message.count() > 0:
                print("✅ 显示创建成功消息")
            else:
                print("⚠️ 未检测到明确的成功指示")
        
        print(f"✅ 知识库创建流程测试完成: {test_name}")
    
    @pytest.mark.asyncio
    async def test_api_integration(self, page: Page):
        """测试前端与后端 API 的集成"""
        # 监听网络请求
        api_requests = []
        
        def handle_request(request):
            if "api/v1" in request.url:
                api_requests.append({
                    "url": request.url,
                    "method": request.method,
                    "headers": dict(request.headers)
                })
        
        page.on("request", handle_request)
        
        # 访问知识库列表页面
        await page.goto("http://localhost:3001/knowledge-bases")
        await page.wait_for_load_state("networkidle")
        
        # 等待一段时间确保 API 请求完成
        await asyncio.sleep(2)
        
        # 检查是否有 API 请求
        assert len(api_requests) > 0, "没有检测到 API 请求"
        
        # 检查是否有获取知识库列表的请求
        list_requests = [req for req in api_requests if "knowledge-bases" in req["url"] and req["method"] == "GET"]
        assert len(list_requests) > 0, "没有检测到获取知识库列表的 API 请求"
        
        # 检查请求头是否包含用户ID
        for req in list_requests:
            if "X-User-ID" in req["headers"]:
                print("✅ API 请求包含用户ID头部")
                break
        else:
            print("⚠️ API 请求可能缺少用户ID头部")
        
        print(f"✅ 检测到 {len(api_requests)} 个 API 请求")
        print(f"✅ 其中 {len(list_requests)} 个是知识库列表请求")
    
    @pytest.mark.asyncio
    async def test_responsive_design(self, page: Page):
        """测试响应式设计"""
        await page.goto("http://localhost:3001/knowledge-bases")
        await page.wait_for_load_state("networkidle")
        
        # 测试桌面视图
        await page.set_viewport_size({"width": 1200, "height": 800})
        await asyncio.sleep(1)
        print("✅ 桌面视图测试完成")
        
        # 测试平板视图
        await page.set_viewport_size({"width": 768, "height": 1024})
        await asyncio.sleep(1)
        print("✅ 平板视图测试完成")
        
        # 测试移动视图
        await page.set_viewport_size({"width": 375, "height": 667})
        await asyncio.sleep(1)
        print("✅ 移动视图测试完成")


if __name__ == "__main__":
    # 运行单个测试的示例
    import subprocess
    import sys
    
    print("🧪 运行前端集成测试...")
    print("请确保前端 (localhost:3001) 和后端 (localhost:8001) 都在运行")
    
    # 运行测试
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        __file__, 
        "-v", 
        "--tb=short"
    ], cwd="/Users/<USER>/Projects/ai/freelancer/education-rag/apps/backend")
    
    sys.exit(result.returncode)
