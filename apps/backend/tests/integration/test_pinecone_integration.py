"""Pinecone 集成测试"""

import asyncio
import os
import uuid
from typing import List

import pytest
from fastapi import HTTPException

from education_rag_backend.schemas.knowledge_base import KnowledgeBaseCreate
from education_rag_backend.services.pinecone_knowledge_base_service import get_pinecone_knowledge_base_service


@pytest.mark.integration
@pytest.mark.pinecone
@pytest.mark.slow
class TestPineconeIntegration:
    """Pinecone 集成测试类
    
    注意: 这些测试需要真实的 Pinecone 连接
    只有在设置了 PINECONE_API_KEY 环境变量时才会运行
    """

    @pytest.fixture(autouse=True)
    def skip_if_no_pinecone_key(self):
        """如果没有 Pinecone API 密钥则跳过测试"""
        if not os.getenv("PINECONE_API_KEY"):
            pytest.skip("需要 PINECONE_API_KEY 环境变量来运行 Pinecone 集成测试")

    @pytest.fixture
    def service(self):
        """获取真实的 Pinecone 服务实例"""
        return get_pinecone_knowledge_base_service()

    @pytest.fixture
    def test_user_id(self):
        """测试用户 ID"""
        return "test-user-" + str(uuid.uuid4())

    @pytest.fixture
    def cleanup_knowledge_bases(self):
        """测试后清理创建的知识库"""
        created_kb_ids: List[str] = []
        
        def add_kb_id(kb_id: str):
            created_kb_ids.append(kb_id)
        
        yield add_kb_id
        
        # 清理
        if created_kb_ids:
            service = get_pinecone_knowledge_base_service()
            for kb_id in created_kb_ids:
                try:
                    # 尝试删除测试创建的知识库
                    vector_id = f"kb_meta_{kb_id}"
                    service.index.delete(ids=[vector_id])
                except Exception:
                    # 忽略清理错误
                    pass

    @pytest.mark.asyncio
    async def test_create_and_retrieve_knowledge_base(self, service, test_user_id, cleanup_knowledge_bases):
        """测试创建和检索知识库的完整流程"""
        # 创建知识库数据
        kb_data = KnowledgeBaseCreate(
            name="集成测试知识库",
            description="用于集成测试的知识库",
            tags=["集成测试", "pinecone", "真实环境"],
            is_public=False
        )
        
        # 创建知识库
        created_kb = await service.create_knowledge_base(
            data=kb_data,
            user_id=test_user_id,
            user_email="<EMAIL>"
        )
        
        # 记录用于清理
        cleanup_knowledge_bases(str(created_kb.id))
        
        # 验证创建结果
        assert created_kb.name == kb_data.name
        assert created_kb.description == kb_data.description
        assert created_kb.tags == kb_data.tags
        assert created_kb.is_public == kb_data.is_public
        assert str(created_kb.owner.id) == test_user_id
        
        # 等待 Pinecone 最终一致性
        await asyncio.sleep(2)
        
        # 检索知识库
        retrieved_kb = await service.get_knowledge_base(
            knowledge_base_id=str(created_kb.id),
            user_id=test_user_id
        )
        
        # 验证检索结果
        assert retrieved_kb.id == created_kb.id
        assert retrieved_kb.name == created_kb.name
        assert retrieved_kb.description == created_kb.description
        assert retrieved_kb.tags == created_kb.tags

    @pytest.mark.asyncio
    async def test_list_knowledge_bases_integration(self, service, test_user_id, cleanup_knowledge_bases):
        """测试知识库列表功能的集成测试"""
        # 创建多个知识库
        kb_data_list = [
            KnowledgeBaseCreate(
                name=f"集成测试知识库 {i}",
                description=f"第 {i} 个集成测试知识库",
                tags=["集成测试", f"测试{i}"],
                is_public=i % 2 == 0  # 偶数为公开
            )
            for i in range(3)
        ]
        
        created_kbs = []
        for kb_data in kb_data_list:
            kb = await service.create_knowledge_base(
                data=kb_data,
                user_id=test_user_id,
                user_email="<EMAIL>"
            )
            created_kbs.append(kb)
            cleanup_knowledge_bases(str(kb.id))
        
        # 等待 Pinecone 最终一致性
        await asyncio.sleep(3)
        
        # 列出知识库
        kbs, total = await service.list_knowledge_bases(
            user_id=test_user_id,
            page=1,
            size=10
        )
        
        # 验证结果 (至少包含我们创建的知识库)
        assert total >= 3
        assert len(kbs) >= 3
        
        # 验证我们创建的知识库都在列表中
        created_kb_ids = {str(kb.id) for kb in created_kbs}
        retrieved_kb_ids = {str(kb.id) for kb in kbs}
        assert created_kb_ids.issubset(retrieved_kb_ids)

    @pytest.mark.asyncio
    async def test_update_knowledge_base_integration(self, service, test_user_id, cleanup_knowledge_bases):
        """测试知识库更新功能的集成测试"""
        # 创建知识库
        kb_data = KnowledgeBaseCreate(
            name="待更新的知识库",
            description="这个知识库将被更新",
            tags=["更新前"],
            is_public=False
        )
        
        created_kb = await service.create_knowledge_base(
            data=kb_data,
            user_id=test_user_id,
            user_email="<EMAIL>"
        )
        cleanup_knowledge_bases(str(created_kb.id))
        
        # 等待 Pinecone 最终一致性
        await asyncio.sleep(2)
        
        # 更新知识库
        from education_rag_backend.schemas.knowledge_base import KnowledgeBaseUpdate
        update_data = KnowledgeBaseUpdate(
            name="已更新的知识库",
            description="这个知识库已经被更新",
            tags=["更新后", "集成测试"],
            is_public=True
        )
        
        updated_kb = await service.update_knowledge_base(
            knowledge_base_id=str(created_kb.id),
            data=update_data,
            user_id=test_user_id
        )
        
        # 验证更新结果
        assert updated_kb.name == update_data.name
        assert updated_kb.description == update_data.description
        assert updated_kb.tags == update_data.tags
        assert updated_kb.is_public == update_data.is_public
        
        # 再次检索验证持久化
        await asyncio.sleep(1)
        retrieved_kb = await service.get_knowledge_base(
            knowledge_base_id=str(created_kb.id),
            user_id=test_user_id
        )
        
        assert retrieved_kb.name == update_data.name
        assert retrieved_kb.description == update_data.description

    @pytest.mark.asyncio
    async def test_delete_knowledge_base_integration(self, service, test_user_id):
        """测试知识库删除功能的集成测试"""
        # 创建知识库
        kb_data = KnowledgeBaseCreate(
            name="待删除的知识库",
            description="这个知识库将被删除",
            tags=["删除测试"],
            is_public=False
        )
        
        created_kb = await service.create_knowledge_base(
            data=kb_data,
            user_id=test_user_id,
            user_email="<EMAIL>"
        )
        
        # 等待 Pinecone 最终一致性
        await asyncio.sleep(2)
        
        # 验证知识库存在
        retrieved_kb = await service.get_knowledge_base(
            knowledge_base_id=str(created_kb.id),
            user_id=test_user_id
        )
        assert retrieved_kb.id == created_kb.id
        
        # 删除知识库
        await service.delete_knowledge_base(
            knowledge_base_id=str(created_kb.id),
            user_id=test_user_id
        )
        
        # 等待删除生效
        await asyncio.sleep(2)
        
        # 验证知识库已被删除
        with pytest.raises(HTTPException) as exc_info:
            await service.get_knowledge_base(
                knowledge_base_id=str(created_kb.id),
                user_id=test_user_id
            )
        assert exc_info.value.status_code == 404

    @pytest.mark.asyncio
    async def test_access_control_integration(self, service, cleanup_knowledge_bases):
        """测试访问控制的集成测试"""
        owner_user_id = "owner-user-" + str(uuid.uuid4())
        other_user_id = "other-user-" + str(uuid.uuid4())
        
        # 创建私有知识库
        kb_data = KnowledgeBaseCreate(
            name="私有知识库",
            description="只有所有者可以访问",
            tags=["私有", "访问控制"],
            is_public=False
        )
        
        created_kb = await service.create_knowledge_base(
            data=kb_data,
            user_id=owner_user_id,
            user_email="<EMAIL>"
        )
        cleanup_knowledge_bases(str(created_kb.id))
        
        # 等待 Pinecone 最终一致性
        await asyncio.sleep(2)
        
        # 所有者可以访问
        owner_retrieved = await service.get_knowledge_base(
            knowledge_base_id=str(created_kb.id),
            user_id=owner_user_id
        )
        assert owner_retrieved.id == created_kb.id
        
        # 其他用户不能访问
        with pytest.raises(HTTPException) as exc_info:
            await service.get_knowledge_base(
                knowledge_base_id=str(created_kb.id),
                user_id=other_user_id
            )
        assert exc_info.value.status_code == 403
        
        # 更新为公开
        from education_rag_backend.schemas.knowledge_base import KnowledgeBaseUpdate
        update_data = KnowledgeBaseUpdate(is_public=True)
        
        await service.update_knowledge_base(
            knowledge_base_id=str(created_kb.id),
            data=update_data,
            user_id=owner_user_id
        )
        
        # 等待更新生效
        await asyncio.sleep(2)
        
        # 现在其他用户也可以访问
        other_retrieved = await service.get_knowledge_base(
            knowledge_base_id=str(created_kb.id),
            user_id=other_user_id
        )
        assert other_retrieved.id == created_kb.id
        assert other_retrieved.is_public is True
