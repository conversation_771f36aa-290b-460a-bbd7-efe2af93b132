"""示例测试文件 - 验证测试环境配置"""

import pytest


@pytest.mark.unit
def test_basic_functionality():
    """基础功能测试"""
    assert 1 + 1 == 2


@pytest.mark.unit
def test_string_operations():
    """字符串操作测试"""
    text = "Education RAG System"
    assert "RAG" in text
    assert text.startswith("Education")
    assert text.endswith("System")


@pytest.mark.unit
def test_list_operations():
    """列表操作测试"""
    tags = ["测试", "pytest", "pinecone"]
    assert len(tags) == 3
    assert "pytest" in tags
    assert tags[0] == "测试"


@pytest.mark.unit
@pytest.mark.asyncio
async def test_async_function():
    """异步函数测试"""
    async def async_add(a, b):
        return a + b
    
    result = await async_add(2, 3)
    assert result == 5


@pytest.mark.unit
def test_mock_usage(mock_settings):
    """测试 mock 使用"""
    assert mock_settings.PINECONE_API_KEY == "test-api-key"
    assert mock_settings.EMBEDDING_DIMENSION == 1024


@pytest.mark.unit
def test_sample_data_fixture(sample_knowledge_base_data):
    """测试示例数据 fixture"""
    assert sample_knowledge_base_data["name"] == "测试知识库"
    assert "测试" in sample_knowledge_base_data["tags"]
    assert sample_knowledge_base_data["is_public"] is False


class TestExampleClass:
    """示例测试类"""
    
    @pytest.mark.unit
    def test_class_method(self):
        """类方法测试"""
        assert True
    
    @pytest.mark.unit
    def test_with_fixture(self, mock_user_id):
        """使用 fixture 的测试"""
        assert mock_user_id == "550e8400-e29b-41d4-a716-446655440000"
