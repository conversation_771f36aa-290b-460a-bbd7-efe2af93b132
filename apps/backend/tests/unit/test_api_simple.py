"""简化的知识库 API 测试"""

import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient


@pytest.mark.unit
@pytest.mark.api
class TestKnowledgeBasesAPISimple:
    """简化的知识库 API 测试类"""

    def test_create_knowledge_base_success(self, client: TestClient, sample_knowledge_base_data):
        """测试成功创建知识库"""
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            # 模拟服务返回
            mock_service = AsyncMock()
            
            # 创建模拟响应对象
            mock_response = MagicMock()
            mock_response.id = uuid.uuid4()
            mock_response.name = sample_knowledge_base_data["name"]
            mock_response.description = sample_knowledge_base_data["description"]
            mock_response.tags = sample_knowledge_base_data["tags"]
            mock_response.is_public = sample_knowledge_base_data["is_public"]
            mock_response.document_count = 0
            mock_response.total_size = 0
            mock_response.owner = MagicMock()
            mock_response.owner.id = uuid.UUID("550e8400-e29b-41d4-a716-************")
            mock_response.owner.email = "<EMAIL>"
            mock_response.created_at = "2025-07-16T10:00:00Z"
            mock_response.updated_at = "2025-07-16T10:00:00Z"
            
            mock_service.create_knowledge_base.return_value = mock_response
            mock_get_service.return_value = mock_service

            response = client.post(
                "/api/v1/knowledge-bases/",
                json=sample_knowledge_base_data
            )

            # 验证响应状态码
            assert response.status_code == status.HTTP_201_CREATED

    def test_create_knowledge_base_validation_error(self, client: TestClient):
        """测试创建知识库验证错误"""
        invalid_data = {
            "name": "",  # 空名称应该失败
            "description": "测试描述"
        }

        response = client.post(
            "/api/v1/knowledge-bases/",
            json=invalid_data
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_list_knowledge_bases_success(self, client: TestClient):
        """测试成功列出知识库"""
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            # 模拟服务返回
            mock_service = AsyncMock()
            mock_service.list_knowledge_bases.return_value = ([], 0)
            mock_get_service.return_value = mock_service

            response = client.get("/api/v1/knowledge-bases/")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total"] == 0
            assert data["page"] == 1
            assert data["size"] == 10
            assert data["pages"] == 0
            assert len(data["items"]) == 0

    def test_get_knowledge_base_success(self, client: TestClient):
        """测试成功获取知识库"""
        kb_id = uuid.uuid4()
        
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            mock_service = AsyncMock()
            
            # 创建模拟响应
            mock_response = MagicMock()
            mock_response.id = kb_id
            mock_response.name = "测试知识库"
            mock_response.description = "测试描述"
            
            mock_service.get_knowledge_base.return_value = mock_response
            mock_get_service.return_value = mock_service

            response = client.get(f"/api/v1/knowledge-bases/{kb_id}")

            assert response.status_code == status.HTTP_200_OK

    def test_get_knowledge_base_not_found(self, client: TestClient):
        """测试获取不存在的知识库"""
        kb_id = uuid.uuid4()
        
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            mock_service = AsyncMock()
            from fastapi import HTTPException
            mock_service.get_knowledge_base.side_effect = HTTPException(
                status_code=404, detail="Knowledge base not found"
            )
            mock_get_service.return_value = mock_service

            response = client.get(f"/api/v1/knowledge-bases/{kb_id}")

            assert response.status_code == status.HTTP_404_NOT_FOUND
