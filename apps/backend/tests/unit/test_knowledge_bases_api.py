"""知识库 API 端点单元测试"""

import json
import uuid
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient


@pytest.mark.unit
@pytest.mark.api
class TestKnowledgeBasesAPI:
    """知识库 API 测试类"""

    def test_create_knowledge_base_success(self, client: TestClient, sample_knowledge_base_data):
        """测试成功创建知识库"""
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            # 模拟服务返回
            mock_service = AsyncMock()

            # 创建模拟响应对象
            mock_response = MagicMock()
            mock_response.id = uuid.uuid4()
            mock_response.name = sample_knowledge_base_data["name"]
            mock_response.description = sample_knowledge_base_data["description"]
            mock_response.tags = sample_knowledge_base_data["tags"]
            mock_response.is_public = sample_knowledge_base_data["is_public"]
            mock_response.document_count = 0
            mock_response.total_size = 0
            mock_response.owner = MagicMock()
            mock_response.owner.id = uuid.UUID("550e8400-e29b-41d4-a716-************")
            mock_response.owner.email = "<EMAIL>"
            mock_response.created_at = "2025-07-16T10:00:00Z"
            mock_response.updated_at = "2025-07-16T10:00:00Z"

            mock_service.create_knowledge_base.return_value = mock_response
            mock_get_service.return_value = mock_service

            response = client.post(
                "/api/v1/knowledge-bases/",
                json=sample_knowledge_base_data
            )

            # 验证响应状态码
            assert response.status_code == status.HTTP_201_CREATED

    def test_create_knowledge_base_validation_error(self, client: TestClient):
        """测试创建知识库验证错误"""
        invalid_data = {
            "name": "",  # 空名称应该失败
            "description": "测试描述"
        }

        response = client.post(
            "/api/v1/knowledge-bases/",
            json=invalid_data
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    def test_list_knowledge_bases_success(self, client: TestClient):
        """测试成功列出知识库"""
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            # 模拟服务返回
            mock_service = AsyncMock()
            mock_service.list_knowledge_bases.return_value = ([], 0)
            mock_get_service.return_value = mock_service

            response = client.get("/api/v1/knowledge-bases/")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total"] == 0
            assert data["page"] == 1
            assert data["size"] == 10
            assert data["pages"] == 0
            assert len(data["items"]) == 0
        """测试成功列出知识库"""
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            # 模拟服务返回
            mock_service = AsyncMock()
            mock_kb = KnowledgeBaseResponse(
                id=uuid.uuid4(),
                name=sample_knowledge_base_data["name"],
                description=sample_knowledge_base_data["description"],
                tags=sample_knowledge_base_data["tags"],
                settings=sample_knowledge_base_data["settings"],
                document_count=0,
                total_size=0,
                is_public=sample_knowledge_base_data["is_public"],
                owner={
                    "id": uuid.UUID("550e8400-e29b-41d4-a716-************"),
                    "email": "<EMAIL>",
                    "username": "test",
                    "full_name": None,
                    "avatar_url": None
                },
                created_at="2025-07-16T10:00:00Z",
                updated_at="2025-07-16T10:00:00Z"
            )
            mock_service.list_knowledge_bases.return_value = ([mock_kb], 1)
            mock_get_service.return_value = mock_service

            response = await async_client.get("/api/v1/knowledge-bases/")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["total"] == 1
            assert data["page"] == 1
            assert data["size"] == 10
            assert data["pages"] == 1
            assert len(data["items"]) == 1
            assert data["items"][0]["name"] == sample_knowledge_base_data["name"]

    @pytest.mark.asyncio
    async def test_list_knowledge_bases_with_filters(self, async_client: AsyncClient):
        """测试带过滤条件的知识库列表"""
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.list_knowledge_bases.return_value = ([], 0)
            mock_get_service.return_value = mock_service

            response = await async_client.get(
                "/api/v1/knowledge-bases/",
                params={
                    "page": 2,
                    "size": 5,
                    "search": "测试",
                    "tags": ["测试", "AI"],
                    "is_public": True
                }
            )

            assert response.status_code == status.HTTP_200_OK
            
            # 验证服务调用参数
            mock_service.list_knowledge_bases.assert_called_once()
            call_args = mock_service.list_knowledge_bases.call_args
            assert call_args.kwargs["page"] == 2
            assert call_args.kwargs["size"] == 5
            assert call_args.kwargs["search"] == "测试"
            assert call_args.kwargs["tags"] == ["测试", "AI"]
            assert call_args.kwargs["is_public"] is True

    @pytest.mark.asyncio
    async def test_get_knowledge_base_success(self, async_client: AsyncClient, sample_knowledge_base_data):
        """测试成功获取知识库"""
        kb_id = uuid.uuid4()
        
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_response = KnowledgeBaseResponse(
                id=kb_id,
                name=sample_knowledge_base_data["name"],
                description=sample_knowledge_base_data["description"],
                tags=sample_knowledge_base_data["tags"],
                settings=sample_knowledge_base_data["settings"],
                document_count=0,
                total_size=0,
                is_public=sample_knowledge_base_data["is_public"],
                owner={
                    "id": uuid.UUID("550e8400-e29b-41d4-a716-************"),
                    "email": "<EMAIL>",
                    "username": "test",
                    "full_name": None,
                    "avatar_url": None
                },
                created_at="2025-07-16T10:00:00Z",
                updated_at="2025-07-16T10:00:00Z"
            )
            mock_service.get_knowledge_base.return_value = mock_response
            mock_get_service.return_value = mock_service

            response = await async_client.get(f"/api/v1/knowledge-bases/{kb_id}")

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["id"] == str(kb_id)
            assert data["name"] == sample_knowledge_base_data["name"]

    @pytest.mark.asyncio
    async def test_get_knowledge_base_not_found(self, async_client: AsyncClient):
        """测试获取不存在的知识库"""
        kb_id = uuid.uuid4()
        
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            mock_service = AsyncMock()
            from fastapi import HTTPException
            mock_service.get_knowledge_base.side_effect = HTTPException(
                status_code=404, detail="Knowledge base not found"
            )
            mock_get_service.return_value = mock_service

            response = await async_client.get(f"/api/v1/knowledge-bases/{kb_id}")

            assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.asyncio
    async def test_update_knowledge_base_success(self, async_client: AsyncClient, sample_knowledge_base_data):
        """测试成功更新知识库"""
        kb_id = uuid.uuid4()
        update_data = {
            "name": "更新后的名称",
            "description": "更新后的描述",
            "tags": ["更新", "测试"]
        }
        
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_response = KnowledgeBaseResponse(
                id=kb_id,
                name=update_data["name"],
                description=update_data["description"],
                tags=update_data["tags"],
                settings=sample_knowledge_base_data["settings"],
                document_count=0,
                total_size=0,
                is_public=False,
                owner={
                    "id": uuid.UUID("550e8400-e29b-41d4-a716-************"),
                    "email": "<EMAIL>",
                    "username": "test",
                    "full_name": None,
                    "avatar_url": None
                },
                created_at="2025-07-16T10:00:00Z",
                updated_at="2025-07-16T10:00:00Z"
            )
            mock_service.update_knowledge_base.return_value = mock_response
            mock_get_service.return_value = mock_service

            response = await async_client.put(
                f"/api/v1/knowledge-bases/{kb_id}",
                json=update_data
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["name"] == update_data["name"]
            assert data["description"] == update_data["description"]
            assert data["tags"] == update_data["tags"]

    @pytest.mark.asyncio
    async def test_delete_knowledge_base_success(self, async_client: AsyncClient):
        """测试成功删除知识库"""
        kb_id = uuid.uuid4()
        
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            mock_service = AsyncMock()
            mock_service.delete_knowledge_base.return_value = None
            mock_get_service.return_value = mock_service

            response = await async_client.delete(f"/api/v1/knowledge-bases/{kb_id}")

            assert response.status_code == status.HTTP_204_NO_CONTENT
            mock_service.delete_knowledge_base.assert_called_once_with(
                knowledge_base_id=str(kb_id),
                user_id="550e8400-e29b-41d4-a716-************"
            )

    @pytest.mark.asyncio
    async def test_delete_knowledge_base_forbidden(self, async_client: AsyncClient):
        """测试删除知识库权限不足"""
        kb_id = uuid.uuid4()
        
        with patch('education_rag_backend.api.routes.knowledge_bases.get_pinecone_knowledge_base_service') as mock_get_service:
            mock_service = AsyncMock()
            from fastapi import HTTPException
            mock_service.delete_knowledge_base.side_effect = HTTPException(
                status_code=403, detail="Only the owner can delete the knowledge base"
            )
            mock_get_service.return_value = mock_service

            response = await async_client.delete(f"/api/v1/knowledge-bases/{kb_id}")

            assert response.status_code == status.HTTP_403_FORBIDDEN
