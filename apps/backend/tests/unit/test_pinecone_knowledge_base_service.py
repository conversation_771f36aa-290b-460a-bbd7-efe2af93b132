"""Pinecone 知识库服务单元测试"""

import json
import uuid
from datetime import datetime
from unittest.mock import MagicMock, patch

import pytest
from fastapi import HTTPException

from education_rag_backend.schemas.knowledge_base import KnowledgeBaseCreate, KnowledgeBaseUpdate
from education_rag_backend.services.pinecone_knowledge_base_service import PineconeKnowledgeBaseService


@pytest.mark.unit
@pytest.mark.service
class TestPineconeKnowledgeBaseService:
    """Pinecone 知识库服务测试类"""

    def test_create_kb_metadata(self, mock_pinecone_service, sample_knowledge_base_data, mock_user_id):
        """测试创建知识库元数据"""
        kb_data = KnowledgeBaseCreate(**sample_knowledge_base_data)
        kb_id = str(uuid.uuid4())
        
        metadata = mock_pinecone_service._create_kb_metadata(
            kb_id=kb_id,
            data=kb_data,
            user_id=mock_user_id,
            user_email="<EMAIL>"
        )
        
        # 验证元数据结构
        assert metadata["type"] == "knowledge_base"
        assert metadata["kb_id"] == kb_id
        assert metadata["name"] == sample_knowledge_base_data["name"]
        assert metadata["description"] == sample_knowledge_base_data["description"]
        assert metadata["owner_id"] == mock_user_id
        assert metadata["is_public"] == str(sample_knowledge_base_data["is_public"]).lower()
        
        # 验证标签 JSON 序列化
        tags = json.loads(metadata["tags"])
        assert tags == sample_knowledge_base_data["tags"]
        
        # 验证时间戳
        assert "created_at" in metadata
        assert "updated_at" in metadata

    def test_metadata_to_response(self, mock_pinecone_service, sample_knowledge_base_metadata):
        """测试元数据转换为响应对象"""
        response = mock_pinecone_service._metadata_to_response(sample_knowledge_base_metadata)
        
        # 验证基本字段
        assert str(response.id) == sample_knowledge_base_metadata["kb_id"]
        assert response.name == sample_knowledge_base_metadata["name"]
        assert response.description == sample_knowledge_base_metadata["description"]
        
        # 验证标签解析
        expected_tags = json.loads(sample_knowledge_base_metadata["tags"])
        assert response.tags == expected_tags
        
        # 验证设置
        assert response.settings["embedding_model"] == sample_knowledge_base_metadata["embedding_model"]
        assert response.settings["chunk_size"] == int(sample_knowledge_base_metadata["chunk_size"])
        
        # 验证所有者信息
        assert str(response.owner.id) == sample_knowledge_base_metadata["owner_id"]
        assert response.owner.email == sample_knowledge_base_metadata["owner_email"]

    def test_check_access_owner(self, mock_pinecone_service, sample_knowledge_base_metadata, mock_user_id):
        """测试所有者访问权限检查"""
        # 设置为所有者
        sample_knowledge_base_metadata["owner_id"] = mock_user_id
        
        has_access = mock_pinecone_service._check_access(sample_knowledge_base_metadata, mock_user_id)
        assert has_access is True

    def test_check_access_public(self, mock_pinecone_service, sample_knowledge_base_metadata):
        """测试公开知识库访问权限检查"""
        # 设置为公开
        sample_knowledge_base_metadata["is_public"] = "true"
        
        has_access = mock_pinecone_service._check_access(sample_knowledge_base_metadata, "other-user-id")
        assert has_access is True

    def test_check_access_denied(self, mock_pinecone_service, sample_knowledge_base_metadata):
        """测试访问权限拒绝"""
        # 设置为私有且非所有者
        sample_knowledge_base_metadata["is_public"] = "false"
        sample_knowledge_base_metadata["owner_id"] = "other-owner-id"
        sample_knowledge_base_metadata["permissions"] = "owner:other-owner-id"  # 不包含测试用户

        has_access = mock_pinecone_service._check_access(sample_knowledge_base_metadata, "user-id")
        assert has_access is False

    @pytest.mark.asyncio
    async def test_create_knowledge_base_success(self, mock_pinecone_service, sample_knowledge_base_data, mock_user_id):
        """测试成功创建知识库"""
        kb_data = KnowledgeBaseCreate(**sample_knowledge_base_data)
        
        # 模拟 upsert 成功
        mock_pinecone_service.index.upsert.return_value = {"upserted_count": 1}
        
        result = await mock_pinecone_service.create_knowledge_base(
            data=kb_data,
            user_id=mock_user_id,
            user_email="<EMAIL>"
        )
        
        # 验证结果
        assert result.name == sample_knowledge_base_data["name"]
        assert result.description == sample_knowledge_base_data["description"]
        assert result.tags == sample_knowledge_base_data["tags"]
        assert str(result.owner.id) == mock_user_id
        
        # 验证 Pinecone 调用
        mock_pinecone_service.index.upsert.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_knowledge_base_failure(self, mock_pinecone_service, sample_knowledge_base_data, mock_user_id):
        """测试创建知识库失败"""
        kb_data = KnowledgeBaseCreate(**sample_knowledge_base_data)
        
        # 模拟 Pinecone 错误
        mock_pinecone_service.index.upsert.side_effect = Exception("Pinecone error")
        
        with pytest.raises(HTTPException) as exc_info:
            await mock_pinecone_service.create_knowledge_base(
                data=kb_data,
                user_id=mock_user_id,
                user_email="<EMAIL>"
            )
        
        assert exc_info.value.status_code == 500
        assert "Failed to create knowledge base" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_get_knowledge_base_success(self, mock_pinecone_service, sample_knowledge_base_metadata, mock_user_id):
        """测试成功获取知识库"""
        kb_id = sample_knowledge_base_metadata["kb_id"]
        vector_id = f"kb_meta_{kb_id}"

        # 确保用户有访问权限 (设置为所有者)
        sample_knowledge_base_metadata["owner_id"] = mock_user_id

        # 模拟 fetch 成功
        mock_vector = MagicMock()
        mock_vector.metadata = sample_knowledge_base_metadata
        mock_pinecone_service.index.fetch.return_value = MagicMock(vectors={vector_id: mock_vector})
        
        result = await mock_pinecone_service.get_knowledge_base(
            knowledge_base_id=kb_id,
            user_id=mock_user_id
        )
        
        # 验证结果
        assert str(result.id) == kb_id
        assert result.name == sample_knowledge_base_metadata["name"]
        
        # 验证 Pinecone 调用
        mock_pinecone_service.index.fetch.assert_called_once_with(ids=[vector_id])

    @pytest.mark.asyncio
    async def test_get_knowledge_base_not_found(self, mock_pinecone_service, mock_user_id):
        """测试获取不存在的知识库"""
        kb_id = "non-existent-id"
        
        # 模拟 fetch 返回空结果
        mock_pinecone_service.index.fetch.return_value = MagicMock(vectors={})
        
        with pytest.raises(HTTPException) as exc_info:
            await mock_pinecone_service.get_knowledge_base(
                knowledge_base_id=kb_id,
                user_id=mock_user_id
            )
        
        assert exc_info.value.status_code == 404
        assert "Knowledge base not found" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_get_knowledge_base_access_denied(self, mock_pinecone_service, sample_knowledge_base_metadata):
        """测试获取知识库访问被拒绝"""
        kb_id = sample_knowledge_base_metadata["kb_id"]
        vector_id = f"kb_meta_{kb_id}"
        
        # 设置为私有且非所有者
        sample_knowledge_base_metadata["is_public"] = "false"
        sample_knowledge_base_metadata["owner_id"] = "other-owner-id"
        
        mock_vector = MagicMock()
        mock_vector.metadata = sample_knowledge_base_metadata
        mock_pinecone_service.index.fetch.return_value = MagicMock(vectors={vector_id: mock_vector})
        
        with pytest.raises(HTTPException) as exc_info:
            await mock_pinecone_service.get_knowledge_base(
                knowledge_base_id=kb_id,
                user_id="unauthorized-user"
            )
        
        assert exc_info.value.status_code == 403
        assert "Access denied" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_list_knowledge_bases_success(self, mock_pinecone_service, sample_knowledge_base_metadata, mock_user_id):
        """测试成功列出知识库"""
        # 确保用户有访问权限 (设置为所有者)
        sample_knowledge_base_metadata["owner_id"] = mock_user_id

        # 模拟查询结果
        mock_match = MagicMock()
        mock_match.metadata = sample_knowledge_base_metadata
        mock_pinecone_service.index.query.return_value = MagicMock(matches=[mock_match])
        
        result, total = await mock_pinecone_service.list_knowledge_bases(
            user_id=mock_user_id,
            page=1,
            size=10
        )
        
        # 验证结果
        assert len(result) == 1
        assert total == 1
        assert str(result[0].id) == sample_knowledge_base_metadata["kb_id"]
        
        # 验证 Pinecone 调用
        mock_pinecone_service.index.query.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_knowledge_base_success(self, mock_pinecone_service, sample_knowledge_base_metadata, mock_user_id):
        """测试成功更新知识库"""
        kb_id = sample_knowledge_base_metadata["kb_id"]
        vector_id = f"kb_meta_{kb_id}"
        
        # 设置为所有者
        sample_knowledge_base_metadata["owner_id"] = mock_user_id
        
        # 模拟 fetch 成功
        mock_vector = MagicMock()
        mock_vector.metadata = sample_knowledge_base_metadata
        mock_pinecone_service.index.fetch.return_value = MagicMock(vectors={vector_id: mock_vector})
        
        # 模拟 upsert 成功
        mock_pinecone_service.index.upsert.return_value = {"upserted_count": 1}
        
        update_data = KnowledgeBaseUpdate(name="更新后的名称", description="更新后的描述")
        
        result = await mock_pinecone_service.update_knowledge_base(
            knowledge_base_id=kb_id,
            data=update_data,
            user_id=mock_user_id
        )
        
        # 验证结果
        assert result.name == "更新后的名称"
        assert result.description == "更新后的描述"
        
        # 验证 Pinecone 调用
        mock_pinecone_service.index.fetch.assert_called_once()
        mock_pinecone_service.index.upsert.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_knowledge_base_success(self, mock_pinecone_service, sample_knowledge_base_metadata, mock_user_id):
        """测试成功删除知识库"""
        kb_id = sample_knowledge_base_metadata["kb_id"]
        vector_id = f"kb_meta_{kb_id}"
        
        # 设置为所有者
        sample_knowledge_base_metadata["owner_id"] = mock_user_id
        
        # 模拟 fetch 成功
        mock_vector = MagicMock()
        mock_vector.metadata = sample_knowledge_base_metadata
        mock_pinecone_service.index.fetch.return_value = MagicMock(vectors={vector_id: mock_vector})
        
        # 模拟 delete 成功
        mock_pinecone_service.index.delete.return_value = None
        
        # 执行删除 (不应该抛出异常)
        await mock_pinecone_service.delete_knowledge_base(
            knowledge_base_id=kb_id,
            user_id=mock_user_id
        )
        
        # 验证 Pinecone 调用
        mock_pinecone_service.index.fetch.assert_called_once()
        mock_pinecone_service.index.delete.assert_called_once_with(ids=[vector_id])

    @pytest.mark.asyncio
    async def test_delete_knowledge_base_not_owner(self, mock_pinecone_service, sample_knowledge_base_metadata):
        """测试删除知识库权限不足"""
        kb_id = sample_knowledge_base_metadata["kb_id"]
        vector_id = f"kb_meta_{kb_id}"
        
        # 设置为其他用户所有
        sample_knowledge_base_metadata["owner_id"] = "other-owner-id"
        
        mock_vector = MagicMock()
        mock_vector.metadata = sample_knowledge_base_metadata
        mock_pinecone_service.index.fetch.return_value = MagicMock(vectors={vector_id: mock_vector})
        
        with pytest.raises(HTTPException) as exc_info:
            await mock_pinecone_service.delete_knowledge_base(
                knowledge_base_id=kb_id,
                user_id="unauthorized-user"
            )
        
        assert exc_info.value.status_code == 403
        assert "Only the owner can delete" in str(exc_info.value.detail)
