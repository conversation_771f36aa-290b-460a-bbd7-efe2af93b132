"""pytest 配置和共享 fixtures"""

import asyncio
import os
import sys
from typing import As<PERSON><PERSON>enerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi.testclient import TestClient
from httpx import AsyncClient

# 添加 src 到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from education_rag_backend.main import create_app
from education_rag_backend.core.config import get_settings


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """创建事件循环用于异步测试"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_settings():
    """模拟配置设置"""
    settings = MagicMock()
    settings.PINECONE_API_KEY = "test-api-key"
    settings.PINECONE_ENVIRONMENT = "us-east-1"
    settings.PINECONE_INDEX_NAME = "test-education-rag"
    settings.EMBEDDING_DIMENSION = 1024
    settings.ZHIPU_API_KEY = "test-zhipu-key"
    settings.ZHIPU_MODEL = "embedding-3"
    settings.DEEPSEEK_API_KEY = "test-deepseek-key"
    settings.SECRET_KEY = "test-secret-key"
    settings.DEBUG = True
    settings.ALLOWED_HOSTS = ["*"]
    return settings


@pytest.fixture
def mock_pinecone_client():
    """模拟 Pinecone 客户端"""
    client = MagicMock()
    
    # 模拟索引列表
    client.list_indexes.return_value = []
    
    # 模拟索引创建
    client.create_index.return_value = None
    
    # 模拟索引对象
    mock_index = MagicMock()
    mock_index.upsert.return_value = {"upserted_count": 1}
    mock_index.fetch.return_value = MagicMock(vectors={})
    mock_index.query.return_value = MagicMock(matches=[])
    mock_index.delete.return_value = None
    
    client.Index.return_value = mock_index
    
    return client


@pytest.fixture
def mock_pinecone_service(mock_settings, mock_pinecone_client):
    """模拟 Pinecone 知识库服务"""
    from education_rag_backend.services.pinecone_knowledge_base_service import PineconeKnowledgeBaseService
    
    service = PineconeKnowledgeBaseService()
    service.settings = mock_settings
    service._pinecone_client = mock_pinecone_client
    service._index = mock_pinecone_client.Index()
    
    return service


@pytest.fixture
def app():
    """创建 FastAPI 应用实例"""
    return create_app()


@pytest.fixture
def client(app):
    """创建测试客户端"""
    return TestClient(app)


@pytest.fixture
async def async_client(app) -> AsyncGenerator[AsyncClient, None]:
    """创建异步测试客户端"""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def sample_knowledge_base_data():
    """示例知识库数据"""
    return {
        "name": "测试知识库",
        "description": "这是一个用于测试的知识库",
        "tags": ["测试", "pytest", "pinecone"],
        "is_public": False,
        "settings": {
            "embedding_model": "embedding-3",
            "chunk_size": 1000,
            "chunk_overlap": 200,
            "retrieval_strategy": "similarity",
            "access_level": "private",
            "auto_processing": True
        }
    }


@pytest.fixture
def sample_knowledge_base_metadata():
    """示例知识库元数据 (Pinecone 格式)"""
    return {
        "type": "knowledge_base",
        "kb_id": "550e8400-e29b-41d4-a716-************",  # 有效的 UUID
        "name": "测试知识库",
        "description": "这是一个用于测试的知识库",
        "tags": '["测试", "pytest", "pinecone"]',
        "embedding_model": "embedding-3",
        "chunk_size": "1000",
        "chunk_overlap": "200",
        "retrieval_strategy": "similarity",
        "access_level": "private",
        "auto_processing": "true",
        "owner_id": "550e8400-e29b-41d4-a716-************",  # 有效的 UUID
        "owner_email": "<EMAIL>",
        "is_public": "false",
        "permissions": "owner:550e8400-e29b-41d4-a716-************",
        "document_count": "0",
        "total_size": "0",
        "created_at": "2025-07-16T10:00:00Z",
        "updated_at": "2025-07-16T10:00:00Z"
    }


@pytest.fixture
def mock_user_id():
    """模拟用户 ID"""
    return "550e8400-e29b-41d4-a716-************"


# 测试标记
pytest_plugins = []

# 自定义标记
def pytest_configure(config):
    """配置自定义标记"""
    config.addinivalue_line("markers", "unit: 单元测试")
    config.addinivalue_line("markers", "integration: 集成测试")
    config.addinivalue_line("markers", "api: API 测试")
    config.addinivalue_line("markers", "service: 服务层测试")
    config.addinivalue_line("markers", "slow: 慢速测试")
    config.addinivalue_line("markers", "pinecone: 需要 Pinecone 连接的测试")
