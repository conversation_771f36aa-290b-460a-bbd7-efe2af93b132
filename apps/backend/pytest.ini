[tool:pytest]
# pytest 配置文件

# 测试发现
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# 输出配置
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    api: API 测试
    service: 服务层测试
    slow: 慢速测试 (通常需要外部服务)
    pinecone: 需要 Pinecone 连接的测试

# 异步测试配置
asyncio_mode = auto

# 最小版本要求
minversion = 6.0

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pydantic.*

# 测试覆盖率配置 (如果使用 pytest-cov)
# addopts = --cov=education_rag_backend --cov-report=html --cov-report=term-missing
