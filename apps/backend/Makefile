# Education RAG Backend Makefile

.PHONY: help test test-unit test-integration test-api test-service test-all test-coverage test-fast install-deps lint format clean

# 默认目标
help:
	@echo "Education RAG Backend 开发工具"
	@echo ""
	@echo "可用命令:"
	@echo "  test              运行单元测试"
	@echo "  test-unit         运行单元测试"
	@echo "  test-integration  运行集成测试"
	@echo "  test-api          运行 API 测试"
	@echo "  test-service      运行服务层测试"
	@echo "  test-all          运行所有测试"
	@echo "  test-coverage     运行测试并生成覆盖率报告"
	@echo "  test-fast         运行快速测试 (跳过慢速测试)"
	@echo "  install-deps      安装测试依赖"
	@echo "  lint              代码检查"
	@echo "  format            代码格式化"
	@echo "  clean             清理临时文件"

# 安装依赖
install-deps:
	@echo "安装测试依赖..."
	uv add --dev pytest pytest-asyncio pytest-mock httpx

# 测试命令
test: test-unit

test-unit:
	@echo "运行单元测试..."
	PYTHONPATH=src python -m pytest tests/ -m "unit" -v

test-integration:
	@echo "运行集成测试..."
	PYTHONPATH=src python -m pytest tests/ -m "integration" -v

test-api:
	@echo "运行 API 测试..."
	PYTHONPATH=src python -m pytest tests/ -m "api" -v

test-service:
	@echo "运行服务层测试..."
	PYTHONPATH=src python -m pytest tests/ -m "service" -v

test-all:
	@echo "运行所有测试..."
	PYTHONPATH=src python -m pytest tests/ -v

test-coverage:
	@echo "运行测试并生成覆盖率报告..."
	PYTHONPATH=src python -m pytest tests/ --cov=src/education_rag_backend --cov-report=html --cov-report=term-missing -v

test-fast:
	@echo "运行快速测试 (跳过慢速测试)..."
	PYTHONPATH=src python -m pytest tests/ -m "not slow" -v

# 特定测试文件
test-pinecone-service:
	@echo "测试 Pinecone 服务..."
	PYTHONPATH=src python -m pytest tests/unit/test_pinecone_knowledge_base_service.py -v

test-api-endpoints:
	@echo "测试 API 端点..."
	PYTHONPATH=src python -m pytest tests/unit/test_knowledge_bases_api.py -v

test-integration-pinecone:
	@echo "测试 Pinecone 集成..."
	PYTHONPATH=src python -m pytest tests/integration/test_pinecone_integration.py -v

# 代码质量
lint:
	@echo "运行代码检查..."
	ruff check src/ tests/
	mypy src/

format:
	@echo "格式化代码..."
	ruff format src/ tests/
	black src/ tests/

# 清理
clean:
	@echo "清理临时文件..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete
	find . -type d -name ".pytest_cache" -exec rm -rf {} + 2>/dev/null || true
	rm -rf htmlcov/
	rm -rf .coverage

# 开发服务器
dev:
	@echo "启动开发服务器..."
	PYTHONPATH=src uvicorn education_rag_backend.main:app --host 0.0.0.0 --port 8001 --reload

# 测试服务器
test-server:
	@echo "启动测试服务器..."
	PYTHONPATH=src python test_pinecone_service.py

# 环境检查
check-env:
	@echo "检查环境配置..."
	@echo "Python 版本: $(shell python --version)"
	@echo "当前目录: $(shell pwd)"
	@echo "PYTHONPATH: $(PYTHONPATH)"
	@if [ -f ".env" ]; then echo "✅ .env 文件存在"; else echo "❌ .env 文件不存在"; fi
	@if [ -d "src" ]; then echo "✅ src 目录存在"; else echo "❌ src 目录不存在"; fi
	@if [ -d "tests" ]; then echo "✅ tests 目录存在"; else echo "❌ tests 目录不存在"; fi

# 快速验证
verify:
	@echo "快速验证测试环境..."
	PYTHONPATH=src python -c "import education_rag_backend; print('✅ 模块导入成功')"
	PYTHONPATH=src python -m pytest tests/test_example.py -v
