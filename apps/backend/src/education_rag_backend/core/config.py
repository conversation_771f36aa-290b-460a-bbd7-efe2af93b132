"""Application configuration settings."""

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    DEBUG: bool = Field(default=False, description="Debug mode")
    SECRET_KEY: str = Field(description="Secret key for JWT tokens")
    ALLOWED_HOSTS: List[str] = Field(default=["*"], description="Allowed hosts")
    
    # Database
    DATABASE_URL: str = Field(
        default="postgresql+asyncpg://postgres:postgres@localhost:5432/education_rag",
        description="Database URL"
    )
    
    # Redis
    REDIS_URL: str = Field(
        default="redis://localhost:6379/0",
        description="Redis URL"
    )
    
    # Pinecone
    PINECONE_API_KEY: str = Field(description="Pinecone API key")
    PINECONE_ENVIRONMENT: str = Field(description="Pinecone environment")
    PINECONE_INDEX_NAME: str = Field(default="education-rag", description="Pinecone index name")
    
    # 智谱AI (Embedding)
    ZHIPU_API_KEY: str = Field(description="智谱AI API key")
    ZHIPU_MODEL: str = Field(default="embedding-3", description="智谱AI embedding model")
    
    # DeepSeek (LLM)
    DEEPSEEK_API_KEY: str = Field(description="DeepSeek API key")
    DEEPSEEK_MODEL: str = Field(default="deepseek-chat", description="DeepSeek model")
    
    # File Storage
    STORAGE_TYPE: str = Field(default="local", description="Storage type: local, s3, minio")
    STORAGE_PATH: str = Field(default="/tmp/education-rag", description="Local storage path")
    STORAGE_BUCKET: str = Field(default="education-rag-documents", description="Storage bucket name")
    STORAGE_REGION: str = Field(default="us-east-1", description="Storage region")
    MAX_FILE_SIZE: int = Field(default=100 * 1024 * 1024, description="Max file size in bytes (100MB)")
    
    # AWS S3 (if using S3)
    AWS_ACCESS_KEY_ID: Optional[str] = Field(default=None, description="AWS access key ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = Field(default=None, description="AWS secret access key")
    
    # MinIO (if using MinIO)
    MINIO_ENDPOINT: str = Field(default="localhost:9000", description="MinIO endpoint")
    MINIO_ACCESS_KEY: str = Field(default="minioadmin", description="MinIO access key")
    MINIO_SECRET_KEY: str = Field(default="minioadmin", description="MinIO secret key")
    MINIO_SECURE: bool = Field(default=False, description="Use HTTPS for MinIO")
    
    # JWT
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    JWT_EXPIRE_MINUTES: int = Field(default=30, description="JWT expiration time in minutes")
    JWT_REFRESH_EXPIRE_DAYS: int = Field(default=7, description="JWT refresh token expiration in days")
    
    # File Upload
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=[".pdf", ".docx", ".doc", ".txt", ".md"],
        description="Allowed file extensions"
    )
    
    # Document Processing
    CHUNK_SIZE: int = Field(default=1000, description="Default chunk size for document processing")
    CHUNK_OVERLAP: int = Field(default=200, description="Default chunk overlap")
    
    # Embedding
    EMBEDDING_DIMENSION: int = Field(default=2048, description="Embedding dimension for ZhipuAI embedding-3")
    
    # Celery (for background tasks)
    CELERY_BROKER_URL: str = Field(
        default="redis://localhost:6379/1",
        description="Celery broker URL"
    )
    CELERY_RESULT_BACKEND: str = Field(
        default="redis://localhost:6379/1",
        description="Celery result backend URL"
    )
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        """Parse allowed hosts from string or list."""
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    @validator("ALLOWED_FILE_TYPES", pre=True)
    def parse_allowed_file_types(cls, v):
        """Parse allowed file types from string or list."""
        if isinstance(v, str):
            return [ext.strip() for ext in v.split(",")]
        return v
    
    class Config:
        """Pydantic config."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
