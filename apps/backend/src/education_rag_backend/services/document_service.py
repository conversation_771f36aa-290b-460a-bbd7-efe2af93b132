"""Document processing service using LangChain."""

import json
import logging
import mimetypes
import uuid
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import tempfile
import os

from fastapi import HTTPException, UploadFile, status
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.document_loaders import (
    PyPDFLoader,
    Docx2txtLoader,
    TextLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader,
)
from langchain.schema import Document as LangChainDocument

from education_rag_backend.core.config import get_settings
from education_rag_backend.schemas.document import (
    DocumentResponse,
    DocumentStatus,
    DocumentType,
    DocumentUploadResponse,
    DocumentListResponse,
)
from education_rag_backend.services.langchain_knowledge_base_service import get_langchain_knowledge_base_service

logger = logging.getLogger(__name__)


class DocumentService:
    """Document processing service with <PERSON><PERSON>hain integration."""
    
    def __init__(self):
        self.settings = get_settings()
        self.kb_service = get_langchain_knowledge_base_service()
        
        # Supported file types
        self.supported_types = {
            "application/pdf": DocumentType.PDF,
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document": DocumentType.DOCX,
            "text/plain": DocumentType.TXT,
            "text/markdown": DocumentType.MD,
            "text/html": DocumentType.HTML,
        }
        
        # File size limits (in bytes)
        self.max_file_size = 50 * 1024 * 1024  # 50MB
    
    def _get_unified_namespace(self, knowledge_base_id: str) -> str:
        """Get unified namespace for both document metadata and content vectors."""
        return f"kb_{knowledge_base_id}"
    
    def _detect_file_type(self, filename: str, content_type: str) -> DocumentType:
        """Detect file type from filename and content type."""
        # First try content type
        if content_type in self.supported_types:
            return self.supported_types[content_type]
        
        # Fallback to file extension
        suffix = Path(filename).suffix.lower()
        extension_map = {
            ".pdf": DocumentType.PDF,
            ".docx": DocumentType.DOCX,
            ".txt": DocumentType.TXT,
            ".md": DocumentType.MD,
            ".html": DocumentType.HTML,
            ".htm": DocumentType.HTML,
        }
        
        if suffix in extension_map:
            return extension_map[suffix]
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported file type: {content_type} ({suffix})"
        )
    
    def _get_document_loader(self, file_path: str, file_type: DocumentType):
        """Get appropriate document loader for file type."""
        loaders = {
            DocumentType.PDF: PyPDFLoader,
            DocumentType.DOCX: Docx2txtLoader,
            DocumentType.TXT: TextLoader,
            DocumentType.MD: UnstructuredMarkdownLoader,
            DocumentType.HTML: UnstructuredHTMLLoader,
        }
        
        if file_type not in loaders:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"No loader available for file type: {file_type}"
            )
        
        return loaders[file_type](file_path)
    
    def _create_document_metadata(
        self, 
        doc_id: str, 
        knowledge_base_id: str, 
        title: str, 
        filename: str, 
        file_type: DocumentType, 
        file_size: int,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create metadata for document."""
        now = datetime.now(timezone.utc)
        
        return {
            "type": "document_metadata",
            "doc_id": doc_id,
            "knowledge_base_id": knowledge_base_id,
            "title": title,
            "description": description or "",
            "filename": filename,
            "file_type": file_type.value,
            "file_size": file_size,
            "status": DocumentStatus.UPLOADING.value,
            "chunk_count": 0,
            "processing_error": "",  # Use empty string instead of None
            "metadata": json.dumps({}),
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "processed_at": ""  # Use empty string instead of None
        }
    
    def _metadata_to_response(self, metadata: Dict[str, Any]) -> DocumentResponse:
        """Convert metadata to response object."""
        # Parse datetime strings
        created_at = metadata["created_at"]
        updated_at = metadata["updated_at"]
        processed_at = metadata.get("processed_at")
        
        if isinstance(created_at, str):
            created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        if isinstance(updated_at, str):
            updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
        if processed_at and isinstance(processed_at, str) and processed_at.strip():
            processed_at = datetime.fromisoformat(processed_at.replace('Z', '+00:00'))
        else:
            processed_at = None
        
        return DocumentResponse(
            id=metadata["doc_id"],
            knowledge_base_id=metadata["knowledge_base_id"],
            title=metadata["title"],
            description=metadata.get("description", ""),
            filename=metadata["filename"],
            file_type=DocumentType(metadata["file_type"]),
            file_size=metadata["file_size"],
            status=DocumentStatus(metadata["status"]),
            chunk_count=metadata.get("chunk_count", 0),
            processing_error=metadata.get("processing_error"),
            metadata=json.loads(metadata.get("metadata", "{}")),
            created_at=created_at,
            updated_at=updated_at,
            processed_at=processed_at
        )
    
    async def upload_document(
        self,
        knowledge_base_id: str,
        file: UploadFile,
        title: Optional[str] = None,
        description: Optional[str] = None,
    ) -> DocumentUploadResponse:
        """Upload and process a document."""
        try:
            # Validate file size
            if file.size and file.size > self.max_file_size:
                raise HTTPException(
                    status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    detail=f"File too large. Maximum size is {self.max_file_size / 1024 / 1024:.1f}MB"
                )
            
            # Detect file type
            content_type = file.content_type or mimetypes.guess_type(file.filename)[0] or "application/octet-stream"
            file_type = self._detect_file_type(file.filename, content_type)
            
            # Generate document ID and use filename as title if not provided
            doc_id = str(uuid.uuid4())
            doc_title = title or Path(file.filename).stem
            
            # Read file content
            file_content = await file.read()
            file_size = len(file_content)
            
            # Create document metadata
            metadata = self._create_document_metadata(
                doc_id=doc_id,
                knowledge_base_id=knowledge_base_id,
                title=doc_title,
                filename=file.filename,
                file_type=file_type,
                file_size=file_size,
                description=description
            )
            
            # Store metadata in unified namespace
            unified_namespace = self._get_unified_namespace(knowledge_base_id)
            
            # Use a small random vector for metadata storage
            import random
            dummy_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]
            
            vector_id = f"doc_metadata_{doc_id}"
            self.kb_service.index.upsert(
                vectors=[{
                    "id": vector_id,
                    "values": dummy_vector,
                    "metadata": metadata
                }],
                namespace=unified_namespace
            )
            
            # Process document in background (for now, we'll do it synchronously)
            # Add a small delay to ensure metadata is stored before processing
            import asyncio
            await asyncio.sleep(0.5)
            await self._process_document(knowledge_base_id, doc_id, file_content, file_type)
            
            return DocumentUploadResponse(
                id=doc_id,
                knowledge_base_id=knowledge_base_id,
                title=doc_title,
                filename=file.filename,
                file_type=file_type,
                file_size=file_size,
                status=DocumentStatus.PROCESSING,
                message="Document uploaded successfully and processing started",
                created_at=datetime.now(timezone.utc)
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading document: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to upload document: {str(e)}"
            )

    async def _process_document(
        self,
        knowledge_base_id: str,
        doc_id: str,
        file_content: bytes,
        file_type: DocumentType
    ) -> None:
        """Process document: extract text, chunk, and vectorize."""
        try:
            # Update status to processing
            await self._update_document_status(knowledge_base_id, doc_id, DocumentStatus.PROCESSING)

            # Save file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=f".{file_type.value}") as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # Load document using appropriate loader
                loader = self._get_document_loader(temp_file_path, file_type)
                documents = loader.load()

                # Get knowledge base settings for chunking
                kb = await self.kb_service.get_knowledge_base(knowledge_base_id)
                chunk_size = kb.settings.get("chunk_size", 1000)
                chunk_overlap = kb.settings.get("chunk_overlap", 200)

                # Split documents into chunks
                text_splitter = RecursiveCharacterTextSplitter(
                    chunk_size=chunk_size,
                    chunk_overlap=chunk_overlap,
                    length_function=len,
                )

                chunks = text_splitter.split_documents(documents)

                # Get vector store for this knowledge base
                vector_store = self.kb_service.get_vector_store(knowledge_base_id)

                # Add document metadata to each chunk
                for i, chunk in enumerate(chunks):
                    chunk.metadata.update({
                        "type": "document_chunk",  # Add type field to distinguish from metadata
                        "document_id": doc_id,
                        "knowledge_base_id": knowledge_base_id,
                        "chunk_index": i,
                        "total_chunks": len(chunks),
                        "file_type": file_type.value,
                        "source": f"doc_{doc_id}_chunk_{i}"
                    })

                # Add chunks to vector store
                if chunks:
                    vector_store.add_documents(chunks)

                # Update document metadata with completion status
                await self._update_document_completion(knowledge_base_id, doc_id, len(chunks))

                logger.info(f"Successfully processed document {doc_id} with {len(chunks)} chunks")

            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

        except Exception as e:
            logger.error(f"Error processing document {doc_id}: {e}")
            await self._update_document_status(
                knowledge_base_id,
                doc_id,
                DocumentStatus.FAILED,
                error_message=str(e)
            )

    async def _update_document_status(
        self,
        knowledge_base_id: str,
        doc_id: str,
        status: DocumentStatus,
        error_message: Optional[str] = None
    ) -> None:
        """Update document processing status."""
        try:
            unified_namespace = self._get_unified_namespace(knowledge_base_id)
            vector_id = f"doc_metadata_{doc_id}"

            # Get current metadata
            result = self.kb_service.index.fetch(
                ids=[vector_id],
                namespace=unified_namespace
            )

            if not result.vectors or vector_id not in result.vectors:
                logger.error(f"Document metadata not found: {doc_id}")
                return

            current_metadata = result.vectors[vector_id].metadata.copy()
            current_metadata["status"] = status.value
            current_metadata["updated_at"] = datetime.now(timezone.utc).isoformat()

            if error_message:
                current_metadata["processing_error"] = error_message
            else:
                current_metadata["processing_error"] = ""

            # Update in Pinecone
            import random
            dummy_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]

            self.kb_service.index.upsert(
                vectors=[{
                    "id": vector_id,
                    "values": dummy_vector,
                    "metadata": current_metadata
                }],
                namespace=unified_namespace
            )

        except Exception as e:
            logger.error(f"Error updating document status: {e}")

    async def _update_document_completion(
        self,
        knowledge_base_id: str,
        doc_id: str,
        chunk_count: int
    ) -> None:
        """Update document with completion status and chunk count."""
        try:
            unified_namespace = self._get_unified_namespace(knowledge_base_id)
            vector_id = f"doc_metadata_{doc_id}"

            # Get current metadata
            result = self.kb_service.index.fetch(
                ids=[vector_id],
                namespace=unified_namespace
            )

            if not result.vectors or vector_id not in result.vectors:
                logger.error(f"Document metadata not found: {doc_id}")
                return

            current_metadata = result.vectors[vector_id].metadata.copy()
            current_metadata["status"] = DocumentStatus.COMPLETED.value
            current_metadata["chunk_count"] = chunk_count
            current_metadata["updated_at"] = datetime.now(timezone.utc).isoformat()
            current_metadata["processed_at"] = datetime.now(timezone.utc).isoformat()

            # Update in Pinecone
            import random
            dummy_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]

            self.kb_service.index.upsert(
                vectors=[{
                    "id": vector_id,
                    "values": dummy_vector,
                    "metadata": current_metadata
                }],
                namespace=unified_namespace
            )

        except Exception as e:
            logger.error(f"Error updating document completion: {e}")

    async def list_documents(
        self,
        knowledge_base_id: str,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None
    ) -> Tuple[List[DocumentListResponse], int]:
        """List documents in a knowledge base."""
        try:
            unified_namespace = self._get_unified_namespace(knowledge_base_id)

            # Build filter - now we need to filter by type to get only document metadata
            filter_dict = {"type": "document_metadata", "knowledge_base_id": knowledge_base_id}

            # Query all document metadata
            import random
            dummy_query_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]

            query_result = self.kb_service.index.query(
                vector=dummy_query_vector,
                filter=filter_dict,
                top_k=1000,  # Get all documents
                include_metadata=True,
                namespace=unified_namespace
            )

            # Process results
            documents = []
            for match in query_result.matches:
                metadata = match.metadata

                # Apply search filter
                if search:
                    search_lower = search.lower()
                    if (search_lower not in metadata.get("title", "").lower() and
                        search_lower not in metadata.get("filename", "").lower() and
                        search_lower not in metadata.get("description", "").lower()):
                        continue

                # Parse datetime
                created_at = metadata["created_at"]
                updated_at = metadata["updated_at"]

                if isinstance(created_at, str):
                    created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                if isinstance(updated_at, str):
                    updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))

                documents.append(DocumentListResponse(
                    id=metadata["doc_id"],
                    title=metadata["title"],
                    filename=metadata["filename"],
                    file_type=DocumentType(metadata["file_type"]),
                    file_size=metadata["file_size"],
                    status=DocumentStatus(metadata["status"]),
                    chunk_count=metadata.get("chunk_count", 0),
                    created_at=created_at,
                    updated_at=updated_at
                ))

            # Sort by updated_at descending
            documents.sort(key=lambda x: x.updated_at.isoformat() if hasattr(x.updated_at, 'isoformat') else str(x.updated_at), reverse=True)

            # Apply pagination
            total = len(documents)
            start_idx = (page - 1) * size
            end_idx = start_idx + size
            paginated_docs = documents[start_idx:end_idx]

            return paginated_docs, total

        except Exception as e:
            logger.error(f"Error listing documents: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list documents: {str(e)}"
            )

    async def get_document(
        self,
        knowledge_base_id: str,
        document_id: str
    ) -> DocumentResponse:
        """Get a document by ID."""
        try:
            unified_namespace = self._get_unified_namespace(knowledge_base_id)
            vector_id = f"doc_metadata_{document_id}"

            # Query for the specific metadata
            result = self.kb_service.index.fetch(
                ids=[vector_id],
                namespace=unified_namespace
            )

            if not result.vectors or vector_id not in result.vectors:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Document not found"
                )

            metadata = result.vectors[vector_id].metadata
            return self._metadata_to_response(metadata)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting document {document_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get document: {str(e)}"
            )

    async def delete_document(
        self,
        knowledge_base_id: str,
        document_id: str
    ) -> None:
        """Delete a document and all its chunks."""
        try:
            # First verify the document exists
            await self.get_document(knowledge_base_id, document_id)

            unified_namespace = self._get_unified_namespace(knowledge_base_id)

            # Delete all document chunks from the knowledge base namespace
            # We need to find and delete all vectors with this document_id
            try:
                # Query for all chunks of this document
                import random
                dummy_query_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]

                query_result = self.kb_service.index.query(
                    vector=dummy_query_vector,
                    filter={"type": "document_chunk", "document_id": document_id},
                    top_k=10000,  # Get all chunks
                    include_metadata=True,
                    namespace=unified_namespace
                )

                # Delete all chunks
                if query_result.matches:
                    chunk_ids = [match.id for match in query_result.matches]
                    self.kb_service.index.delete(ids=chunk_ids, namespace=unified_namespace)
                    logger.info(f"Deleted {len(chunk_ids)} chunks for document {document_id}")

            except Exception as e:
                logger.warning(f"Error deleting document chunks: {e}")

            # Delete document metadata
            vector_id = f"doc_metadata_{document_id}"
            self.kb_service.index.delete(ids=[vector_id], namespace=unified_namespace)

            logger.info(f"Deleted document {document_id}")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete document: {str(e)}"
            )


# Service instance
_service_instance = None

def get_document_service() -> DocumentService:
    """Get singleton instance of the document service."""
    global _service_instance
    if _service_instance is None:
        _service_instance = DocumentService()
    return _service_instance
