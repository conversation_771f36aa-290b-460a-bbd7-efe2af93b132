"""Services module."""

# Import the new Pinecone-based service (lazy initialization)
from education_rag_backend.services.pinecone_knowledge_base_service import get_pinecone_knowledge_base_service

# Keep the old services for backward compatibility (but don't auto-import to avoid env var issues)
# from education_rag_backend.services.knowledge_base_service import knowledge_base_service
# from education_rag_backend.services.langchain_service import langchain_service

__all__ = [
    "get_pinecone_knowledge_base_service",
    # "knowledge_base_service",
    # "langchain_service",
]
