"""Pinecone-based knowledge base service."""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, <PERSON>, Optional, Tuple

from fastapi import HTTP<PERSON>x<PERSON>, status
from pinecone import Pinecone

from education_rag_backend.core.config import get_settings
from education_rag_backend.schemas.knowledge_base import (
    KnowledgeBaseCreate,
    KnowledgeBaseResponse,
    KnowledgeBaseSettings,
    KnowledgeBaseUpdate,
)

logger = logging.getLogger(__name__)


class PineconeKnowledgeBaseService:
    """Service for knowledge base operations using Pinecone."""
    
    def __init__(self):
        self.settings = get_settings()
        self._pinecone_client = None
        self._index = None
    
    @property
    def pinecone_client(self) -> Pinecone:
        """Get Pinecone client."""
        if self._pinecone_client is None:
            self._pinecone_client = Pinecone(api_key=self.settings.PINECONE_API_KEY)
        return self._pinecone_client
    
    @property
    def index(self):
        """Get Pinecone index."""
        if self._index is None:
            # Check if index exists, create if it doesn't
            self._ensure_index_exists()
            self._index = self.pinecone_client.Index(self.settings.PINECONE_INDEX_NAME)
        return self._index

    def _ensure_index_exists(self):
        """Ensure the Pinecone index exists, create if it doesn't."""
        try:
            # Check if index exists
            existing_indexes = self.pinecone_client.list_indexes()
            index_names = [idx.name for idx in existing_indexes]

            if self.settings.PINECONE_INDEX_NAME not in index_names:
                logger.info(f"Creating Pinecone index: {self.settings.PINECONE_INDEX_NAME}")

                from pinecone import ServerlessSpec

                # Create the index
                self.pinecone_client.create_index(
                    name=self.settings.PINECONE_INDEX_NAME,
                    dimension=self.settings.EMBEDDING_DIMENSION,
                    metric="cosine",
                    spec=ServerlessSpec(
                        cloud="aws",
                        region=self.settings.PINECONE_ENVIRONMENT
                    )
                )
                logger.info(f"Successfully created Pinecone index: {self.settings.PINECONE_INDEX_NAME}")
            else:
                logger.info(f"Pinecone index already exists: {self.settings.PINECONE_INDEX_NAME}")

        except Exception as e:
            logger.error(f"Error ensuring Pinecone index exists: {e}")
            raise
    
    def _create_kb_metadata(self, kb_id: str, data: KnowledgeBaseCreate) -> Dict:
        """Create metadata for knowledge base."""
        now = datetime.utcnow().isoformat() + "Z"
        
        # Create default settings if not provided
        if not data.settings:
            data.settings = KnowledgeBaseSettings()
        
        return {
            # Core Knowledge Base Info
            "type": "knowledge_base",
            "kb_id": kb_id,
            "name": data.name,
            "description": data.description or "",

            # Settings
            "embedding_model": data.settings.embedding_model,
            "chunk_size": str(data.settings.chunk_size),
            "chunk_overlap": str(data.settings.chunk_overlap),
            "retrieval_strategy": data.settings.retrieval_strategy,
            "access_level": data.settings.access_level,
            "auto_processing": str(data.settings.auto_processing),

            # Permissions
            "is_public": str(data.is_public).lower(),

            # Statistics
            "document_count": "0",
            "total_size": "0",

            # Timestamps
            "created_at": now,
            "updated_at": now
        }
    
    def _metadata_to_response(self, metadata: Dict) -> KnowledgeBaseResponse:
        """Convert Pinecone metadata to response schema."""
        # Create settings dict
        settings = {
            "embedding_model": metadata.get("embedding_model", "embedding-3"),
            "chunk_size": int(metadata.get("chunk_size", "1000")),
            "chunk_overlap": int(metadata.get("chunk_overlap", "200")),
            "retrieval_strategy": metadata.get("retrieval_strategy", "similarity"),
            "access_level": metadata.get("access_level", "private"),
            "auto_processing": metadata.get("auto_processing", "true").lower() == "true"
        }

        return KnowledgeBaseResponse(
            id=uuid.UUID(metadata["kb_id"]),
            name=metadata["name"],
            description=metadata.get("description"),
            settings=settings,
            document_count=int(metadata.get("document_count", "0")),
            total_size=int(metadata.get("total_size", "0")),
            is_public=metadata.get("is_public", "false").lower() == "true",
            created_at=datetime.fromisoformat(metadata["created_at"].replace("Z", "+00:00")),
            updated_at=datetime.fromisoformat(metadata["updated_at"].replace("Z", "+00:00"))
        )
    
    async def create_knowledge_base(self, data: KnowledgeBaseCreate) -> KnowledgeBaseResponse:
        """Create a new knowledge base in Pinecone."""
        try:
            kb_id = str(uuid.uuid4())
            
            # Create metadata
            metadata = self._create_kb_metadata(kb_id, data)
            
            # Create a dummy vector for the knowledge base metadata
            # We use a small random vector since Pinecone doesn't allow all-zero vectors
            import random
            dummy_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]
            
            # Upsert to Pinecone with a special ID for knowledge base metadata
            vector_id = f"kb_meta_{kb_id}"
            
            self.index.upsert(
                vectors=[{
                    "id": vector_id,
                    "values": dummy_vector,
                    "metadata": metadata
                }]
            )
            
            logger.info(f"Created knowledge base {kb_id} in Pinecone")
            
            # Return the response
            return self._metadata_to_response(metadata)
            
        except Exception as e:
            logger.error(f"Error creating knowledge base: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create knowledge base: {str(e)}"
            )
    
    async def get_knowledge_base(self, knowledge_base_id: str) -> KnowledgeBaseResponse:
        """Get a knowledge base by ID."""
        try:
            vector_id = f"kb_meta_{knowledge_base_id}"
            
            # Fetch from Pinecone
            result = self.index.fetch(ids=[vector_id])

            logger.info(f"Fetch result for {vector_id}: {list(result.vectors.keys())}")

            if vector_id not in result.vectors:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Knowledge base not found"
                )
            
            metadata = result.vectors[vector_id].metadata
            
            # No permission check needed
            
            return self._metadata_to_response(metadata)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting knowledge base: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get knowledge base: {str(e)}"
            )
    

    
    async def list_knowledge_bases(
        self,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        is_public: Optional[bool] = None,
    ) -> Tuple[List[KnowledgeBaseResponse], int]:
        """List knowledge bases."""
        try:
            # Build filter for knowledge base metadata
            filter_dict = {"type": {"$eq": "knowledge_base"}}
            
            # Add public filter if specified
            if is_public is not None:
                filter_dict["is_public"] = {"$eq": str(is_public).lower()}
            
            # Query Pinecone for knowledge base metadata
            # Note: Pinecone has limitations on complex filtering, so we'll fetch and filter in memory
            import random
            dummy_query_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]
            query_result = self.index.query(
                vector=dummy_query_vector,
                filter=filter_dict,
                top_k=1000,  # Fetch more than needed, then filter
                include_metadata=True
            )

            logger.info(f"Pinecone query returned {len(query_result.matches)} matches")
            
            knowledge_bases = []
            for match in query_result.matches:
                metadata = match.metadata
                
                # No access control needed
                
                # Apply search filter
                if search:
                    search_lower = search.lower()
                    if (search_lower not in metadata.get("name", "").lower() and 
                        search_lower not in metadata.get("description", "").lower()):
                        continue
                
                # No tags filter needed
                
                knowledge_bases.append(self._metadata_to_response(metadata))
            
            # Sort by created_at (newest first)
            knowledge_bases.sort(key=lambda x: x.created_at, reverse=True)
            
            # Apply pagination
            total = len(knowledge_bases)
            start = (page - 1) * size
            end = start + size
            paginated_kbs = knowledge_bases[start:end]
            
            return paginated_kbs, total
            
        except Exception as e:
            logger.error(f"Error listing knowledge bases: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list knowledge bases: {str(e)}"
            )


    async def update_knowledge_base(
        self,
        knowledge_base_id: str,
        data: KnowledgeBaseUpdate,
    ) -> KnowledgeBaseResponse:
        """Update a knowledge base."""
        try:
            vector_id = f"kb_meta_{knowledge_base_id}"

            # First, get the existing knowledge base
            result = self.index.fetch(ids=[vector_id])

            if vector_id not in result.vectors:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Knowledge base not found"
                )

            existing_metadata = result.vectors[vector_id].metadata

            # No ownership check needed

            # Update metadata
            updated_metadata = existing_metadata.copy()
            updated_metadata["updated_at"] = datetime.utcnow().isoformat() + "Z"

            if data.name is not None:
                updated_metadata["name"] = data.name

            if data.description is not None:
                updated_metadata["description"] = data.description

            # Tags removed

            if data.is_public is not None:
                updated_metadata["is_public"] = str(data.is_public).lower()

            if data.settings is not None:
                updated_metadata["embedding_model"] = data.settings.embedding_model
                updated_metadata["chunk_size"] = str(data.settings.chunk_size)
                updated_metadata["chunk_overlap"] = str(data.settings.chunk_overlap)
                updated_metadata["retrieval_strategy"] = data.settings.retrieval_strategy
                updated_metadata["access_level"] = data.settings.access_level
                updated_metadata["auto_processing"] = str(data.settings.auto_processing)

            # Update in Pinecone
            import random
            dummy_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]
            self.index.upsert(
                vectors=[{
                    "id": vector_id,
                    "values": dummy_vector,
                    "metadata": updated_metadata
                }]
            )

            logger.info(f"Updated knowledge base {knowledge_base_id}")

            return self._metadata_to_response(updated_metadata)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating knowledge base: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update knowledge base: {str(e)}"
            )

    async def delete_knowledge_base(self, knowledge_base_id: str) -> None:
        """Delete a knowledge base."""
        try:
            vector_id = f"kb_meta_{knowledge_base_id}"

            # First, get the existing knowledge base to check ownership
            result = self.index.fetch(ids=[vector_id])

            if vector_id not in result.vectors:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Knowledge base not found"
                )

            existing_metadata = result.vectors[vector_id].metadata

            # No ownership check needed

            # Delete the metadata vector
            self.index.delete(ids=[vector_id])

            # TODO: Also delete all document vectors for this knowledge base
            # This would require querying for all vectors with kb_id metadata
            # and deleting them in batches

            logger.info(f"Deleted knowledge base {knowledge_base_id}")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting knowledge base: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete knowledge base: {str(e)}"
            )


# Create singleton instance (lazy initialization)
_pinecone_knowledge_base_service = None

def get_pinecone_knowledge_base_service() -> PineconeKnowledgeBaseService:
    """Get the singleton instance of PineconeKnowledgeBaseService."""
    global _pinecone_knowledge_base_service
    if _pinecone_knowledge_base_service is None:
        _pinecone_knowledge_base_service = PineconeKnowledgeBaseService()
    return _pinecone_knowledge_base_service
