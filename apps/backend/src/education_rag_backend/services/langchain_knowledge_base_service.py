"""LangChain-based knowledge base service with Pinecone namespace support."""

import json
import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple, Any

from fastapi import HTTPException, status
from pinecone import Pinecone
from langchain_pinecone import PineconeVectorStore
from langchain_community.embeddings import ZhipuAIEmbeddings

from education_rag_backend.core.config import get_settings
from education_rag_backend.schemas.knowledge_base import (
    KnowledgeBaseCreate,
    KnowledgeBaseResponse,
    KnowledgeBaseUpdate,
)

logger = logging.getLogger(__name__)


class LangChainKnowledgeBaseService:
    """LangChain-based knowledge base service with proper namespace management."""
    
    def __init__(self):
        self.settings = get_settings()
        self._pinecone_client = None
        self._embeddings = None
        self._index = None
        
    @property
    def pinecone_client(self) -> Pinecone:
        """Get Pinecone client."""
        if self._pinecone_client is None:
            self._pinecone_client = Pinecone(api_key=self.settings.PINECONE_API_KEY)
        return self._pinecone_client
    
    @property
    def embeddings(self) -> ZhipuAIEmbeddings:
        """Get ZhipuAI embeddings."""
        if self._embeddings is None:
            self._embeddings = ZhipuAIEmbeddings(
                api_key=self.settings.ZHIPU_API_KEY,
                model="embedding-3"
            )
        return self._embeddings
    
    @property
    def index(self):
        """Get Pinecone index."""
        if self._index is None:
            self._ensure_index_exists()
            self._index = self.pinecone_client.Index(self.settings.PINECONE_INDEX_NAME)
        return self._index
    
    def _ensure_index_exists(self) -> None:
        """Ensure the Pinecone index exists."""
        try:
            existing_indexes = [index.name for index in self.pinecone_client.list_indexes()]
            
            if self.settings.PINECONE_INDEX_NAME not in existing_indexes:
                logger.info(f"Creating Pinecone index: {self.settings.PINECONE_INDEX_NAME} with dimension {self.settings.EMBEDDING_DIMENSION}")
                self.pinecone_client.create_index(
                    name=self.settings.PINECONE_INDEX_NAME,
                    dimension=self.settings.EMBEDDING_DIMENSION,  # Now 2048 for ZhipuAI embedding-3
                    metric="cosine",
                    spec={
                        "serverless": {
                            "cloud": "aws",
                            "region": "us-east-1"
                        }
                    }
                )
                logger.info(f"Index created successfully with dimension {self.settings.EMBEDDING_DIMENSION}")
            else:
                logger.info(f"Index {self.settings.PINECONE_INDEX_NAME} already exists")
                
        except Exception as e:
            logger.error(f"Error ensuring index exists: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to initialize Pinecone index: {str(e)}"
            )
    
    def _get_namespace_name(self, kb_id: str) -> str:
        """Generate namespace name for a knowledge base."""
        return f"kb_{kb_id}"
    
    def _get_metadata_namespace(self) -> str:
        """Get namespace for knowledge base metadata."""
        return "kb_metadata"
    
    def _create_kb_metadata(self, kb_id: str, data: KnowledgeBaseCreate) -> Dict[str, Any]:
        """Create metadata for knowledge base."""
        now = datetime.now(timezone.utc)

        # Handle settings properly
        settings_dict = {}
        if data.settings:
            if hasattr(data.settings, 'model_dump'):
                settings_dict = data.settings.model_dump()
            elif hasattr(data.settings, 'dict'):
                settings_dict = data.settings.dict()
            else:
                settings_dict = data.settings

        return {
            "type": "knowledge_base_metadata",
            "kb_id": kb_id,
            "name": data.name,
            "description": data.description or "",
            "is_public": data.is_public,
            "settings": json.dumps(settings_dict),
            "created_at": now.isoformat(),
            "updated_at": now.isoformat(),
            "document_count": 0,
            "total_size": 0
        }
    
    def _metadata_to_response(self, metadata: Dict[str, Any]) -> KnowledgeBaseResponse:
        """Convert metadata to response object."""
        settings_dict = json.loads(metadata.get("settings", "{}"))

        # Parse datetime strings
        created_at = metadata["created_at"]
        updated_at = metadata["updated_at"]

        if isinstance(created_at, str):
            created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
        if isinstance(updated_at, str):
            updated_at = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))

        return KnowledgeBaseResponse(
            id=metadata["kb_id"],
            name=metadata["name"],
            description=metadata.get("description", ""),
            settings=settings_dict,  # Return as dict, not KnowledgeBaseSettings object
            document_count=metadata.get("document_count", 0),
            total_size=metadata.get("total_size", 0),
            is_public=metadata.get("is_public", False),
            created_at=created_at,
            updated_at=updated_at
        )
    
    def get_vector_store(self, kb_id: str) -> PineconeVectorStore:
        """Get LangChain vector store for a specific knowledge base."""
        namespace = self._get_namespace_name(kb_id)
        return PineconeVectorStore(
            index=self.index,
            embedding=self.embeddings,
            namespace=namespace
        )
    
    async def create_knowledge_base(self, data: KnowledgeBaseCreate) -> KnowledgeBaseResponse:
        """Create a new knowledge base with its own namespace."""
        try:
            kb_id = str(uuid.uuid4())
            namespace = self._get_namespace_name(kb_id)
            metadata_namespace = self._get_metadata_namespace()
            
            # Create metadata
            metadata = self._create_kb_metadata(kb_id, data)
            
            # Store metadata in special metadata namespace
            # Use a small random vector for metadata storage
            import random
            dummy_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]
            
            vector_id = f"metadata_{kb_id}"
            self.index.upsert(
                vectors=[{
                    "id": vector_id,
                    "values": dummy_vector,
                    "metadata": metadata
                }],
                namespace=metadata_namespace
            )
            
            logger.info(f"Created knowledge base {kb_id} with namespace {namespace}")
            return self._metadata_to_response(metadata)
            
        except Exception as e:
            logger.error(f"Error creating knowledge base: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create knowledge base: {str(e)}"
            )
    
    async def get_knowledge_base(self, knowledge_base_id: str) -> KnowledgeBaseResponse:
        """Get a knowledge base by ID."""
        try:
            metadata_namespace = self._get_metadata_namespace()
            vector_id = f"metadata_{knowledge_base_id}"
            
            # Query for the specific metadata
            result = self.index.fetch(
                ids=[vector_id],
                namespace=metadata_namespace
            )
            
            if not result.vectors or vector_id not in result.vectors:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Knowledge base not found"
                )
            
            metadata = result.vectors[vector_id].metadata
            return self._metadata_to_response(metadata)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting knowledge base {knowledge_base_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get knowledge base: {str(e)}"
            )
    
    async def list_knowledge_bases(
        self,
        page: int = 1,
        size: int = 10,
        search: Optional[str] = None,
        is_public: Optional[bool] = None
    ) -> Tuple[List[KnowledgeBaseResponse], int]:
        """List knowledge bases with pagination and filtering."""
        try:
            metadata_namespace = self._get_metadata_namespace()
            
            # Build filter
            filter_dict = {"type": "knowledge_base_metadata"}
            if is_public is not None:
                filter_dict["is_public"] = is_public
            
            # Query all metadata
            import random
            dummy_query_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]
            
            query_result = self.index.query(
                vector=dummy_query_vector,
                filter=filter_dict,
                top_k=1000,  # Get all metadata
                include_metadata=True,
                namespace=metadata_namespace
            )
            
            # Process results
            knowledge_bases = []
            for match in query_result.matches:
                metadata = match.metadata
                
                # Apply search filter
                if search:
                    search_lower = search.lower()
                    if (search_lower not in metadata.get("name", "").lower() and 
                        search_lower not in metadata.get("description", "").lower()):
                        continue
                
                knowledge_bases.append(self._metadata_to_response(metadata))
            
            # Sort by updated_at descending (as string, ISO format sorts correctly)
            knowledge_bases.sort(key=lambda x: x.updated_at.isoformat() if hasattr(x.updated_at, 'isoformat') else str(x.updated_at), reverse=True)
            
            # Apply pagination
            total = len(knowledge_bases)
            start_idx = (page - 1) * size
            end_idx = start_idx + size
            paginated_kbs = knowledge_bases[start_idx:end_idx]
            
            return paginated_kbs, total

        except Exception as e:
            logger.error(f"Error listing knowledge bases: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to list knowledge bases: {str(e)}"
            )

    async def update_knowledge_base(
        self,
        knowledge_base_id: str,
        data: KnowledgeBaseUpdate
    ) -> KnowledgeBaseResponse:
        """Update a knowledge base."""
        try:
            # First verify the knowledge base exists
            await self.get_knowledge_base(knowledge_base_id)

            metadata_namespace = self._get_metadata_namespace()
            vector_id = f"metadata_{knowledge_base_id}"

            # Get current metadata
            result = self.index.fetch(
                ids=[vector_id],
                namespace=metadata_namespace
            )

            if not result.vectors or vector_id not in result.vectors:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Knowledge base not found"
                )

            current_metadata = result.vectors[vector_id].metadata.copy()

            # Update metadata
            if data.name is not None:
                current_metadata["name"] = data.name
            if data.description is not None:
                current_metadata["description"] = data.description
            if data.is_public is not None:
                current_metadata["is_public"] = data.is_public
            if data.settings is not None:
                if hasattr(data.settings, 'model_dump'):
                    current_metadata["settings"] = json.dumps(data.settings.model_dump())
                elif hasattr(data.settings, 'dict'):
                    current_metadata["settings"] = json.dumps(data.settings.dict())
                else:
                    current_metadata["settings"] = json.dumps(data.settings)

            current_metadata["updated_at"] = datetime.now(timezone.utc).isoformat()

            # Update in Pinecone
            import random
            dummy_vector = [random.uniform(-0.01, 0.01) for _ in range(self.settings.EMBEDDING_DIMENSION)]

            self.index.upsert(
                vectors=[{
                    "id": vector_id,
                    "values": dummy_vector,
                    "metadata": current_metadata
                }],
                namespace=metadata_namespace
            )

            logger.info(f"Updated knowledge base {knowledge_base_id}")
            return self._metadata_to_response(current_metadata)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating knowledge base {knowledge_base_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update knowledge base: {str(e)}"
            )

    async def delete_knowledge_base(self, knowledge_base_id: str) -> None:
        """Delete a knowledge base and all its documents."""
        try:
            # First verify the knowledge base exists
            await self.get_knowledge_base(knowledge_base_id)

            metadata_namespace = self._get_metadata_namespace()
            kb_namespace = self._get_namespace_name(knowledge_base_id)

            # Delete all vectors in the knowledge base namespace
            # Note: Pinecone doesn't have a direct way to delete all vectors in a namespace
            # We need to delete by filter or delete the entire namespace
            try:
                # Delete all vectors in the knowledge base namespace
                self.index.delete(delete_all=True, namespace=kb_namespace)
                logger.info(f"Deleted all documents in namespace {kb_namespace}")
            except Exception as e:
                logger.warning(f"Error deleting documents in namespace {kb_namespace}: {e}")

            # Delete metadata
            vector_id = f"metadata_{knowledge_base_id}"
            self.index.delete(ids=[vector_id], namespace=metadata_namespace)

            logger.info(f"Deleted knowledge base {knowledge_base_id}")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting knowledge base {knowledge_base_id}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete knowledge base: {str(e)}"
            )

    async def get_knowledge_base_stats(self, knowledge_base_id: str) -> Dict[str, Any]:
        """Get statistics for a knowledge base."""
        try:
            kb_namespace = self._get_namespace_name(knowledge_base_id)

            # Get namespace stats
            stats = self.index.describe_index_stats()
            namespace_stats = stats.namespaces.get(kb_namespace, {})

            return {
                "document_count": namespace_stats.get("vector_count", 0),
                "total_size": 0,  # This would need to be calculated from document metadata
                "namespace": kb_namespace
            }

        except Exception as e:
            logger.error(f"Error getting stats for knowledge base {knowledge_base_id}: {e}")
            return {
                "document_count": 0,
                "total_size": 0,
                "namespace": self._get_namespace_name(knowledge_base_id)
            }


# Service instance
_service_instance = None

def get_langchain_knowledge_base_service() -> LangChainKnowledgeBaseService:
    """Get singleton instance of the LangChain knowledge base service."""
    global _service_instance
    if _service_instance is None:
        _service_instance = LangChainKnowledgeBaseService()
    return _service_instance
