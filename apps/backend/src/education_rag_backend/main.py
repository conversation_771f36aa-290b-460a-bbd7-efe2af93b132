"""FastAPI application entry point."""

import logging
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware

from education_rag_backend.api.routes import api_router
from education_rag_backend.core.config import get_settings
from education_rag_backend.core.database import init_db

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan manager."""
    logger.info("Starting Education RAG Backend...")

    # Skip database initialization since we're using Pinecone
    # await init_db()
    logger.info("Pinecone-based backend initialized")

    yield

    logger.info("Shutting down Education RAG Backend...")


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="Education RAG Backend",
        description="A comprehensive RAG management system with knowledge base management and document processing",
        version="0.1.0",
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
        lifespan=lifespan,
    )
    
    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_HOSTS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include API routes
    app.include_router(api_router, prefix="/api/v1")
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {"status": "healthy", "version": "0.1.0"}
    
    return app


# Create the FastAPI app instance
app = create_app()

if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info",
    )
