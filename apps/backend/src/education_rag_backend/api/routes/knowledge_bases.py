"""Knowledge base API routes."""

import uuid
from typing import List, Optional

from fastapi import APIRouter, HTTPException, Query, status

from education_rag_backend.schemas.knowledge_base import (
    KnowledgeBaseCreate,
    KnowledgeBaseResponse,
    KnowledgeBaseUpdate,
    PaginatedKnowledgeBaseResponse,
)
from education_rag_backend.services.pinecone_knowledge_base_service import get_pinecone_knowledge_base_service

router = APIRouter(prefix="/knowledge-bases", tags=["knowledge-bases"])


@router.post("/", response_model=KnowledgeBaseResponse, status_code=status.HTTP_201_CREATED)
async def create_knowledge_base(data: KnowledgeBaseCreate):
    """Create a new knowledge base."""
    service = get_pinecone_knowledge_base_service()
    knowledge_base = await service.create_knowledge_base(data=data)
    return knowledge_base


@router.get("/", response_model=PaginatedKnowledgeBaseResponse)
async def list_knowledge_bases(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Page size"),
    search: Optional[str] = Query(None, description="Search query"),
    is_public: Optional[bool] = Query(None, description="Filter by public/private"),
):
    """List knowledge bases."""
    service = get_pinecone_knowledge_base_service()
    knowledge_bases, total = await service.list_knowledge_bases(
        page=page,
        size=size,
        search=search,
        is_public=is_public
    )

    pages = (total + size - 1) // size

    return PaginatedKnowledgeBaseResponse(
        items=knowledge_bases,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{knowledge_base_id}", response_model=KnowledgeBaseResponse)
async def get_knowledge_base(knowledge_base_id: uuid.UUID):
    """Get a knowledge base by ID."""
    service = get_pinecone_knowledge_base_service()
    knowledge_base = await service.get_knowledge_base(knowledge_base_id=str(knowledge_base_id))
    return knowledge_base


@router.put("/{knowledge_base_id}", response_model=KnowledgeBaseResponse)
async def update_knowledge_base(
    knowledge_base_id: uuid.UUID,
    data: KnowledgeBaseUpdate,
):
    """Update a knowledge base."""
    service = get_pinecone_knowledge_base_service()
    knowledge_base = await service.update_knowledge_base(
        knowledge_base_id=str(knowledge_base_id), data=data
    )
    return knowledge_base


@router.delete("/{knowledge_base_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_knowledge_base(knowledge_base_id: uuid.UUID):
    """Delete a knowledge base."""
    service = get_pinecone_knowledge_base_service()
    await service.delete_knowledge_base(knowledge_base_id=str(knowledge_base_id))
    return None
