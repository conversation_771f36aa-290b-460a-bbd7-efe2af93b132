"""LangChain-based knowledge base API routes."""

import uuid
from typing import List, Optional

from fastapi import APIRouter, File, Form, HTTPException, Query, UploadFile, status

from education_rag_backend.schemas.knowledge_base import (
    KnowledgeBaseCreate,
    KnowledgeBaseResponse,
    KnowledgeBaseUpdate,
    PaginatedKnowledgeBaseResponse,
)
from education_rag_backend.schemas.document import (
    DocumentResponse,
    DocumentUploadResponse,
    PaginatedDocumentResponse,
)
from education_rag_backend.services.langchain_knowledge_base_service import get_langchain_knowledge_base_service
from education_rag_backend.services.document_service import get_document_service

router = APIRouter(prefix="/langchain-knowledge-bases", tags=["langchain-knowledge-bases"])


@router.post("/", response_model=KnowledgeBaseResponse, status_code=status.HTTP_201_CREATED)
async def create_knowledge_base(data: KnowledgeBaseCreate):
    """Create a new knowledge base using LangChain with namespace support."""
    service = get_langchain_knowledge_base_service()
    knowledge_base = await service.create_knowledge_base(data=data)
    return knowledge_base


@router.get("/", response_model=PaginatedKnowledgeBaseResponse)
async def list_knowledge_bases(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Page size"),
    search: Optional[str] = Query(None, description="Search query"),
    is_public: Optional[bool] = Query(None, description="Filter by public/private"),
):
    """List knowledge bases using LangChain service."""
    service = get_langchain_knowledge_base_service()
    knowledge_bases, total = await service.list_knowledge_bases(
        page=page,
        size=size,
        search=search,
        is_public=is_public
    )

    pages = (total + size - 1) // size

    return PaginatedKnowledgeBaseResponse(
        items=knowledge_bases,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{knowledge_base_id}", response_model=KnowledgeBaseResponse)
async def get_knowledge_base(knowledge_base_id: uuid.UUID):
    """Get a knowledge base by ID using LangChain service."""
    service = get_langchain_knowledge_base_service()
    knowledge_base = await service.get_knowledge_base(knowledge_base_id=str(knowledge_base_id))
    return knowledge_base


@router.put("/{knowledge_base_id}", response_model=KnowledgeBaseResponse)
async def update_knowledge_base(
    knowledge_base_id: uuid.UUID,
    data: KnowledgeBaseUpdate,
):
    """Update a knowledge base using LangChain service."""
    service = get_langchain_knowledge_base_service()
    knowledge_base = await service.update_knowledge_base(
        knowledge_base_id=str(knowledge_base_id), data=data
    )
    return knowledge_base


@router.delete("/{knowledge_base_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_knowledge_base(knowledge_base_id: uuid.UUID):
    """Delete a knowledge base using LangChain service."""
    service = get_langchain_knowledge_base_service()
    await service.delete_knowledge_base(knowledge_base_id=str(knowledge_base_id))


@router.get("/{knowledge_base_id}/stats")
async def get_knowledge_base_stats(knowledge_base_id: uuid.UUID):
    """Get statistics for a knowledge base."""
    service = get_langchain_knowledge_base_service()
    stats = await service.get_knowledge_base_stats(knowledge_base_id=str(knowledge_base_id))
    return stats


@router.get("/{knowledge_base_id}/vector-store")
async def get_vector_store_info(knowledge_base_id: uuid.UUID):
    """Get vector store information for a knowledge base."""
    service = get_langchain_knowledge_base_service()
    
    # First verify the knowledge base exists
    await service.get_knowledge_base(knowledge_base_id=str(knowledge_base_id))
    
    # Get vector store info
    namespace = service._get_namespace_name(str(knowledge_base_id))
    stats = await service.get_knowledge_base_stats(knowledge_base_id=str(knowledge_base_id))
    
    return {
        "knowledge_base_id": str(knowledge_base_id),
        "namespace": namespace,
        "vector_count": stats.get("document_count", 0),
        "index_name": service.settings.PINECONE_INDEX_NAME,
        "embedding_model": "embedding-3",
        "dimension": service.settings.EMBEDDING_DIMENSION
    }


# Document management endpoints
@router.post("/{knowledge_base_id}/documents/upload", response_model=DocumentUploadResponse)
async def upload_document(
    knowledge_base_id: uuid.UUID,
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
):
    """Upload a document to a knowledge base."""
    # First verify the knowledge base exists
    kb_service = get_langchain_knowledge_base_service()
    await kb_service.get_knowledge_base(knowledge_base_id=str(knowledge_base_id))

    # Upload and process the document
    doc_service = get_document_service()
    result = await doc_service.upload_document(
        knowledge_base_id=str(knowledge_base_id),
        file=file,
        title=title,
        description=description
    )

    return result


# Document management endpoints
@router.post("/{knowledge_base_id}/documents/upload", response_model=DocumentUploadResponse)
async def upload_document(
    knowledge_base_id: uuid.UUID,
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
):
    """Upload a document to a knowledge base."""
    # First verify the knowledge base exists
    kb_service = get_langchain_knowledge_base_service()
    await kb_service.get_knowledge_base(knowledge_base_id=str(knowledge_base_id))

    # Upload and process the document
    doc_service = get_document_service()
    result = await doc_service.upload_document(
        knowledge_base_id=str(knowledge_base_id),
        file=file,
        title=title,
        description=description
    )

    return result


@router.get("/{knowledge_base_id}/documents", response_model=PaginatedDocumentResponse)
async def list_documents(
    knowledge_base_id: uuid.UUID,
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(10, ge=1, le=100, description="Page size"),
    search: Optional[str] = Query(None, description="Search query"),
):
    """List documents in a knowledge base."""
    # First verify the knowledge base exists
    kb_service = get_langchain_knowledge_base_service()
    await kb_service.get_knowledge_base(knowledge_base_id=str(knowledge_base_id))

    # Get documents
    doc_service = get_document_service()
    documents, total = await doc_service.list_documents(
        knowledge_base_id=str(knowledge_base_id),
        page=page,
        size=size,
        search=search
    )

    pages = (total + size - 1) // size

    return PaginatedDocumentResponse(
        items=documents,
        total=total,
        page=page,
        size=size,
        pages=pages
    )


@router.get("/{knowledge_base_id}/documents/{document_id}", response_model=DocumentResponse)
async def get_document(
    knowledge_base_id: uuid.UUID,
    document_id: uuid.UUID,
):
    """Get a document by ID."""
    # First verify the knowledge base exists
    kb_service = get_langchain_knowledge_base_service()
    await kb_service.get_knowledge_base(knowledge_base_id=str(knowledge_base_id))

    # Get document
    doc_service = get_document_service()
    document = await doc_service.get_document(
        knowledge_base_id=str(knowledge_base_id),
        document_id=str(document_id)
    )

    return document


@router.delete("/{knowledge_base_id}/documents/{document_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_document(
    knowledge_base_id: uuid.UUID,
    document_id: uuid.UUID,
):
    """Delete a document from a knowledge base."""
    # First verify the knowledge base exists
    kb_service = get_langchain_knowledge_base_service()
    await kb_service.get_knowledge_base(knowledge_base_id=str(knowledge_base_id))

    # Delete document
    doc_service = get_document_service()
    await doc_service.delete_document(
        knowledge_base_id=str(knowledge_base_id),
        document_id=str(document_id)
    )
