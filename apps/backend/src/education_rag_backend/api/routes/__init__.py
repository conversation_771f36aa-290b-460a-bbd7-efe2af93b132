"""API routes."""

from fastapi import APIRouter

from education_rag_backend.api.routes.knowledge_bases import router as knowledge_bases_router
from education_rag_backend.api.routes.langchain_knowledge_bases import router as langchain_knowledge_bases_router

api_router = APIRouter()

# Original Pinecone service (for backward compatibility)
api_router.include_router(knowledge_bases_router)

# New LangChain-based service with namespace support
api_router.include_router(langchain_knowledge_bases_router)
