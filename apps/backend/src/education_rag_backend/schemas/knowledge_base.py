"""Knowledge base schemas."""

from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class KnowledgeBaseSettings(BaseModel):
    """Knowledge base settings schema."""
    
    embedding_model: str = Field(default="embedding-3", description="Embedding model name")
    chunk_size: int = Field(default=1000, description="Document chunk size")
    chunk_overlap: int = Field(default=200, description="Chunk overlap size")
    retrieval_strategy: str = Field(default="similarity", description="Retrieval strategy")
    access_level: str = Field(default="private", description="Access level: public, private, restricted")
    auto_processing: bool = Field(default=True, description="Auto process uploaded documents")


class KnowledgeBaseCreate(BaseModel):
    """Schema for creating a knowledge base."""

    name: str = Field(..., min_length=1, max_length=255, description="Knowledge base name")
    description: Optional[str] = Field(None, description="Knowledge base description")
    settings: Optional[KnowledgeBaseSettings] = Field(default=None, description="Knowledge base settings")
    is_public: bool = Field(default=False, description="Whether the knowledge base is public")


class KnowledgeBaseUpdate(BaseModel):
    """Schema for updating a knowledge base."""

    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Knowledge base name")
    description: Optional[str] = Field(None, description="Knowledge base description")
    settings: Optional[KnowledgeBaseSettings] = Field(None, description="Knowledge base settings")
    is_public: Optional[bool] = Field(None, description="Whether the knowledge base is public")


class KnowledgeBaseResponse(BaseModel):
    """Schema for knowledge base response."""

    id: UUID
    name: str
    description: Optional[str]
    settings: Dict
    document_count: int
    total_size: int
    is_public: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class KnowledgeBaseListResponse(BaseModel):
    """Schema for knowledge base list response."""

    id: UUID
    name: str
    description: Optional[str]
    document_count: int
    total_size: int
    is_public: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PaginatedKnowledgeBaseResponse(BaseModel):
    """Schema for paginated knowledge base response."""

    items: List[KnowledgeBaseResponse]
    total: int
    page: int
    size: int
    pages: int


class KnowledgeBaseStats(BaseModel):
    """Schema for knowledge base statistics."""
    
    total_knowledge_bases: int
    total_documents: int
    total_size: int
    public_knowledge_bases: int
    private_knowledge_bases: int
