"""Document schemas."""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class DocumentStatus(str, Enum):
    """Document processing status."""
    UPLOADING = "uploading"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class DocumentType(str, Enum):
    """Document type."""
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    MD = "md"
    HTML = "html"


class DocumentChunk(BaseModel):
    """Document chunk schema."""
    id: str
    content: str
    metadata: Dict
    page_number: Optional[int] = None
    chunk_index: int
    start_char: Optional[int] = None
    end_char: Optional[int] = None


class DocumentResponse(BaseModel):
    """Schema for document response."""
    id: UUID
    knowledge_base_id: UUID
    title: str
    description: Optional[str]
    filename: str
    file_type: DocumentType
    file_size: int
    status: DocumentStatus
    chunk_count: int
    processing_error: Optional[str] = None
    metadata: Dict = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime
    processed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class DocumentUploadResponse(BaseModel):
    """Schema for document upload response."""
    id: UUID
    knowledge_base_id: UUID
    title: str
    filename: str
    file_type: DocumentType
    file_size: int
    status: DocumentStatus
    message: str
    created_at: datetime


class DocumentListResponse(BaseModel):
    """Schema for document list item response."""
    id: UUID
    title: str
    filename: str
    file_type: DocumentType
    file_size: int
    status: DocumentStatus
    chunk_count: int
    created_at: datetime
    updated_at: datetime


class PaginatedDocumentResponse(BaseModel):
    """Schema for paginated document response."""
    items: List[DocumentListResponse]
    total: int
    page: int
    size: int
    pages: int


class DocumentSearchRequest(BaseModel):
    """Schema for document search request."""
    query: str
    top_k: int = Field(default=5, ge=1, le=50)
    score_threshold: float = Field(default=0.7, ge=0.0, le=1.0)
    include_metadata: bool = Field(default=True)


class DocumentSearchResult(BaseModel):
    """Schema for document search result."""
    chunk_id: str
    document_id: UUID
    document_title: str
    content: str
    score: float
    metadata: Dict
    page_number: Optional[int] = None
    chunk_index: int


class DocumentSearchResponse(BaseModel):
    """Schema for document search response."""
    query: str
    results: List[DocumentSearchResult]
    total_results: int
    search_time_ms: float


class DocumentProcessingStats(BaseModel):
    """Schema for document processing statistics."""
    total_documents: int
    processing_documents: int
    completed_documents: int
    failed_documents: int
    total_chunks: int
    total_size_bytes: int
    average_processing_time_seconds: Optional[float] = None
