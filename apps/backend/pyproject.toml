[project]
name = "education-rag-backend"
version = "0.1.0"
description = "FastAPI backend for Education RAG Management System"
readme = "README.md"
authors = [
    { name = "Education RAG Team", email = "<EMAIL>" }
]
requires-python = ">=3.10"
dependencies = [
    # FastAPI and server
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "gunicorn>=21.2.0",
    "python-multipart>=0.0.6",  # Required for file uploads
    # Database
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "asyncpg>=0.29.0", # PostgreSQL async driver
    "redis>=5.0.0",
    "pgvector>=0.2.0",
    # Vector Database
    "pinecone>=6.0.0", # 注意：已从pinecone-client更名为pinecone
    # RAG Framework
    "langgraph>=0.3.0",
    "langchain>=0.3.0",
    "langchain-openai>=0.1.0",
    "langchain-community>=0.2.19",
    "langchain-pinecone>=0.1.0",
    "langchain-deepseek>=0.1.3", # 支持DeepSeek API
    # Document Processing
    "unstructured[all-docs]>=0.11.0",
    "python-docx>=1.1.0",
    "pymupdf>=1.23.0",
    "pytesseract>=0.3.10",
    "pillow>=10.0.0",
    # ML and Embeddings
    "openai>=1.3.0",
    "tiktoken>=0.5.0",
    "zhipuai>=2.1.0", # 智谱AI SDK
    # Authentication and Security
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    # Utilities
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-dotenv>=1.0.0",
    "httpx>=0.25.0",
    "aiofiles>=23.2.0",
    "celery>=5.3.0",
    "flower>=2.0.0",
    # Monitoring and Logging
    "structlog>=23.2.0",
    "prometheus-client>=0.19.0",
]

[project.optional-dependencies]
dev = [
    # Testing
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",  # for testing FastAPI
    "factory-boy>=3.3.0",

    # Code Quality
    "ruff>=0.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",

    # Development Tools
    "ipython>=8.17.0",
    "rich>=13.7.0",
    "typer>=0.9.0",
]

[project.scripts]
education-rag-backend = "education_rag_backend.main:app"

[tool.hatch.build.targets.wheel]
packages = ["src/education_rag_backend"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
