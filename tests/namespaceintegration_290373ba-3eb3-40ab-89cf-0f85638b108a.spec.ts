
import { test } from '@playwright/test';
import { expect } from '@playwright/test';

test('NamespaceIntegration_2025-07-17', async ({ page, context }) => {
  
    // Navigate to URL
    await page.goto('http://localhost:3001');

    // Take screenshot
    await page.screenshot({ path: 'homepage.png' });

    // Click element
    await page.click('a[href="/knowledge-bases"]');

    // Take screenshot
    await page.screenshot({ path: 'knowledge-bases-list.png' });

    // Click element
    await page.click('a[href="/knowledge-bases/create"]');

    // Take screenshot
    await page.screenshot({ path: 'create-knowledge-base.png' });

    // Fill input field
    await page.fill('input[name="name"]', '前端集成测试知识库');

    // Fill input field
    await page.fill('textarea[name="description"]', '用于测试新的统一namespace架构与前端集成的知识库');

    // Click element
    await page.click('button[type="submit"]');

    // Take screenshot
    await page.screenshot({ path: 'knowledge-base-created.png' });

    // Take screenshot
    await page.screenshot({ path: 'knowledge-base-detail-scrolled.png' });

    // Click element
    await page.click('.border-dashed');

    // Click element
    await page.click('text=前端集成测试知识库');

    // Take screenshot
    await page.screenshot({ path: 'knowledge-base-detail-page.png' });

    // Take screenshot
    await page.screenshot({ path: 'document-uploaded.png' });

    // Take screenshot
    await page.screenshot({ path: 'document-status-updated.png' });
});