import { test, expect } from '@playwright/test';

test.describe('知识库创建功能', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到知识库页面
    await page.goto('http://localhost:3001/knowledge-bases');
  });

  test('应该能够导航到创建知识库页面', async ({ page }) => {
    // 点击创建知识库按钮
    await page.click('text=创建知识库');
    
    // 验证页面标题
    await expect(page).toHaveURL(/.*\/knowledge-bases\/create/);
    await expect(page.locator('h1')).toContainText('创建知识库');
  });

  test('应该能够成功创建一个知识库', async ({ page }) => {
    // 导航到创建页面
    await page.goto('http://localhost:3001/knowledge-bases/create');
    
    // 填写表单
    await page.fill('input[name="name"]', '测试知识库');
    await page.fill('textarea[name="description"]', '这是一个用于测试的知识库');
    
    // 添加标签
    await page.fill('input[placeholder="添加标签"]', '测试');
    await page.click('button[type="button"]:has(svg)'); // 点击添加标签按钮
    
    await page.fill('input[placeholder="添加标签"]', '自动化');
    await page.press('input[placeholder="添加标签"]', 'Enter'); // 使用回车键添加标签
    
    // 验证标签已添加
    await expect(page.locator('[data-testid="tag-测试"]')).toBeVisible();
    await expect(page.locator('[data-testid="tag-自动化"]')).toBeVisible();
    
    // 设置为公开
    await page.click('button[role="switch"]');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 验证成功创建（应该重定向到知识库列表页面）
    await expect(page).toHaveURL(/.*\/knowledge-bases$/, { timeout: 10000 });

    // 验证新创建的知识库出现在列表中
    await expect(page.locator('text=测试知识库').first()).toBeVisible({ timeout: 10000 });
  });

  test('应该验证必填字段', async ({ page }) => {
    // 导航到创建页面
    await page.goto('http://localhost:3001/knowledge-bases/create');
    
    // 尝试提交空表单
    await page.click('button[type="submit"]');
    
    // 验证错误消息
    await expect(page.locator('text=知识库名称不能为空')).toBeVisible();
  });

  test('应该能够删除标签', async ({ page }) => {
    // 导航到创建页面
    await page.goto('http://localhost:3001/knowledge-bases/create');
    
    // 添加标签
    await page.fill('input[placeholder="添加标签"]', '临时标签');
    await page.press('input[placeholder="添加标签"]', 'Enter');
    
    // 验证标签已添加
    await expect(page.locator('text=临时标签')).toBeVisible();
    
    // 删除标签
    await page.click('[data-testid="remove-tag-临时标签"]');
    
    // 验证标签已删除
    await expect(page.locator('text=临时标签')).not.toBeVisible();
  });

  test('应该能够取消创建并返回列表页面', async ({ page }) => {
    // 导航到创建页面
    await page.goto('http://localhost:3001/knowledge-bases/create');
    
    // 点击取消按钮
    await page.click('text=取消');
    
    // 验证返回到知识库列表页面
    await expect(page).toHaveURL(/.*\/knowledge-bases$/);
  });

  test('应该显示表单验证错误', async ({ page }) => {
    // 导航到创建页面
    await page.goto('http://localhost:3001/knowledge-bases/create');
    
    // 输入过长的名称
    const longName = 'a'.repeat(300);
    await page.fill('input[name="name"]', longName);
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 验证长度验证错误
    await expect(page.locator('text=知识库名称不能超过255个字符')).toBeVisible();
  });

  test('应该正确处理公开/私有切换', async ({ page }) => {
    // 导航到创建页面
    await page.goto('http://localhost:3001/knowledge-bases/create');
    
    // 验证默认状态（私有）
    const switchButton = page.locator('button[role="switch"]');
    await expect(switchButton).toHaveAttribute('data-state', 'unchecked');
    
    // 切换到公开
    await switchButton.click();
    await expect(switchButton).toHaveAttribute('data-state', 'checked');
    
    // 再次切换回私有
    await switchButton.click();
    await expect(switchButton).toHaveAttribute('data-state', 'unchecked');
  });

  test('应该能够处理网络错误', async ({ page }) => {
    // 拦截API请求并返回错误
    await page.route('**/api/v1/knowledge-bases', route => {
      route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: { message: 'Internal Server Error' } })
      });
    });
    
    // 导航到创建页面
    await page.goto('http://localhost:3001/knowledge-bases/create');
    
    // 填写表单
    await page.fill('input[name="name"]', '测试知识库');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 验证错误消息显示
    await expect(page.locator('text=创建失败')).toBeVisible();
    await expect(page.locator('text=创建知识库时出现错误，请稍后重试').first()).toBeVisible();
  });

  test('应该显示加载状态', async ({ page }) => {
    // 拦截API请求并延迟响应
    await page.route('**/api/v1/knowledge-bases', route => {
      setTimeout(() => {
        route.fulfill({
          status: 201,
          contentType: 'application/json',
          body: JSON.stringify({
            data: {
              id: '123',
              name: '测试知识库',
              description: '测试描述',
              tags: [],
              is_public: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
          })
        });
      }, 2000);
    });
    
    // 导航到创建页面
    await page.goto('http://localhost:3001/knowledge-bases/create');
    
    // 填写表单
    await page.fill('input[name="name"]', '测试知识库');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 验证加载状态
    await expect(page.locator('button[type="submit"]')).toBeDisabled();
    await expect(page.locator('svg.animate-spin')).toBeVisible();
  });
});
