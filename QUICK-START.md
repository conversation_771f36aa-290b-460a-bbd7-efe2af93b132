# 🚀 Education RAG 系统快速启动指南

## 📋 系统概览

**当前状态**: 系统简化完成，前后端完全集成，Playwright 测试验证通过
**完成度**: 75%
**最后更新**: 2025-07-16

## 🏗️ 系统架构

```
前端 (Next.js)     ←→     后端 (FastAPI)     ←→     Pinecone 向量数据库
localhost:3002            localhost:8001            云端服务
```

## ⚡ 快速启动

### 1. 启动后端服务

```bash
cd apps/backend
source .venv/bin/activate
PYTHONPATH=src uvicorn education_rag_backend.main:app --host 0.0.0.0 --port 8001 --reload
```

**验证后端**: 访问 http://localhost:8001/docs

### 2. 启动前端服务

```bash
cd apps/frontend
npm run dev
```

**验证前端**: 访问 http://localhost:3002

### 3. 验证集成

```bash
cd apps/backend
python test_integration.py
```

## 🧪 测试系统

### 运行所有测试

```bash
cd apps/backend
make test-all
```

### 运行特定测试

```bash
# 单元测试
make test-unit

# API 测试
make test-api-endpoints

# Pinecone 服务测试
make test-pinecone-service

# 集成测试
make test-integration
```

### 前端测试

```bash
cd apps/frontend
npm test
```

## 🎯 核心功能验证

### 1. 知识库管理
- ✅ 访问 http://localhost:3002/knowledge-bases
- ✅ 创建新知识库（简化表单，无标签）
- ✅ 查看知识库列表
- ✅ 编辑知识库信息
- ✅ 删除知识库
- ✅ 设置公开/私有权限

### 2. 集成测试
- ✅ 访问 http://localhost:3002/test-integration
- ✅ 测试前后端 API 连接
- ✅ 验证知识库创建功能
- ✅ 检查数据格式兼容性

### 2. API 功能
- ✅ GET /api/v1/knowledge-bases/ (获取列表)
- ✅ POST /api/v1/knowledge-bases/ (创建)
- ✅ GET /api/v1/knowledge-bases/{id} (获取详情)
- ✅ PUT /api/v1/knowledge-bases/{id} (更新)
- ✅ DELETE /api/v1/knowledge-bases/{id} (删除)

## 🔧 开发工具

### 后端开发

```bash
# 代码格式化
cd apps/backend
black src/
isort src/

# 类型检查
mypy src/

# 代码质量检查
flake8 src/
```

### 前端开发

```bash
cd apps/frontend

# 代码格式化
npm run format

# 类型检查
npm run type-check

# 代码检查
npm run lint
```

## 📊 系统监控

### 后端日志
- 服务器日志: 终端输出
- API 请求日志: FastAPI 自动记录
- 错误日志: 控制台和日志文件

### 前端日志
- 浏览器控制台: 开发者工具
- 网络请求: 开发者工具 Network 标签
- React 组件: React DevTools

## 🐛 常见问题

### 端口冲突
```bash
# 查看端口占用
lsof -i :8001  # 后端
lsof -i :3001  # 前端

# 杀死进程
kill <PID>
```

### 依赖问题
```bash
# 后端依赖
cd apps/backend
pip install -r requirements.txt

# 前端依赖
cd apps/frontend
npm install
```

### 环境变量
```bash
# 后端环境变量
cp apps/backend/.env.example apps/backend/.env
# 编辑 .env 文件，添加 Pinecone API 密钥

# 前端环境变量
cp apps/frontend/.env.local.example apps/frontend/.env.local
```

## 🎯 下次开发重点

### 即将实现的功能
1. **文档上传功能**
   - 文件上传界面
   - 支持多种文档格式
   - 文档预处理和分块

2. **向量化处理**
   - 文档内容向量化
   - 存储到 Pinecone
   - 向量索引管理

3. **语义搜索**
   - 基于 Pinecone 的相似性搜索
   - 搜索结果排序和过滤
   - 搜索历史记录

4. **RAG 问答**
   - 智能问答界面
   - 上下文检索
   - 答案生成和引用

### 技术债务
1. 实现真实的用户认证系统
2. 添加更完善的错误处理
3. 优化性能和缓存机制
4. 完善文档和注释

## 📚 相关文档

- [实现状态](docs/IMPLEMENTATION-STATUS.md)
- [开发日志](docs/DEVELOPMENT-LOG.md)
- [开发指南](docs/DEVELOPMENT-GUIDE.md)
- [API 文档](http://localhost:8001/docs)

## 🎉 成功指标

当你看到以下内容时，说明系统运行正常：

1. **后端**: `INFO: Application startup complete.`
2. **前端**: `✓ Ready in X.Xs`
3. **集成测试**: `🎉 前后端集成测试全部通过！`
4. **浏览器**: 能够正常访问和操作知识库

---

**祝开发愉快！** 🚀
