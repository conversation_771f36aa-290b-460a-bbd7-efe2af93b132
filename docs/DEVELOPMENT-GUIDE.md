# Education RAG 开发指南

## 🚀 快速开始

### 环境要求

- **Node.js**: 18+ (推荐使用 nvm)
- **Python**: 3.12+
- **uv**: 最新版本
- **Docker**: 最新版本
- **PostgreSQL**: 15+ (或使用 Docker)
- **Redis**: 7+ (或使用 Docker)

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd education-rag

# 运行自动化设置脚本
npm run setup

# 或手动安装
npm install                    # 安装前端依赖
cd apps/backend && uv sync     # 安装后端依赖
```

### 环境配置

```bash
# 复制环境配置文件
cp apps/frontend/.env.example apps/frontend/.env.local
cp apps/backend/.env.example apps/backend/.env

# 编辑配置文件
# apps/frontend/.env.local
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_WS_URL=ws://localhost:8000/ws

# apps/backend/.env
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/education_rag
REDIS_URL=redis://localhost:6379/0
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_ENVIRONMENT=your_pinecone_environment
OPENAI_API_KEY=your_openai_api_key
SECRET_KEY=your_secret_key_here
```

### 启动开发环境

```bash
# 启动所有服务（推荐）
npm run dev

# 或分别启动
npm run dev:services    # 启动数据库服务
npm run dev:backend     # 启动后端API
npm run dev:frontend    # 启动前端应用
```

### 访问应用

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **数据库**: localhost:5432
- **Redis**: localhost:6379

## 🏗️ 项目结构

```
education-rag/
├── apps/
│   ├── frontend/              # Next.js 前端应用
│   │   ├── src/
│   │   │   ├── app/           # App Router 页面
│   │   │   ├── components/    # React 组件
│   │   │   ├── hooks/         # 自定义 Hooks
│   │   │   ├── lib/           # 工具库
│   │   │   ├── services/      # API 服务
│   │   │   └── types/         # TypeScript 类型
│   │   ├── public/            # 静态资源
│   │   └── package.json
│   └── backend/               # FastAPI 后端应用
│       ├── src/education_rag_backend/
│       │   ├── api/           # API 路由
│       │   ├── core/          # 核心配置
│       │   ├── models/        # 数据模型
│       │   ├── services/      # 业务逻辑
│       │   ├── schemas/       # Pydantic 模式
│       │   └── utils/         # 工具函数
│       ├── tests/             # 测试文件
│       ├── alembic/           # 数据库迁移
│       └── pyproject.toml
├── packages/
│   ├── types/                 # 共享类型定义
│   ├── utils/                 # 共享工具函数
│   └── config/                # 共享配置
├── docker/                    # Docker 配置
├── scripts/                   # 开发脚本
├── docs/                      # 项目文档
└── package.json               # 根 package.json
```

## 🔧 开发工作流

### 代码规范

#### 前端 (TypeScript/React)

```bash
# 代码检查
npm run lint:frontend

# 代码格式化
npm run format:frontend

# 类型检查
npm run type-check:frontend

# 运行测试
npm run test:frontend
```

**编码规范**:
- 使用 TypeScript 严格模式
- 组件使用函数式组件 + Hooks
- 使用 Tailwind CSS 进行样式设计
- 遵循 React 最佳实践

#### 后端 (Python/FastAPI)

```bash
# 代码检查
npm run lint:backend

# 代码格式化
npm run format:backend

# 类型检查
npm run type-check:backend

# 运行测试
npm run test:backend
```

**编码规范**:
- 使用 Python 3.12+ 特性
- 遵循 PEP 8 代码风格
- 使用类型注解
- 编写文档字符串

### Git 工作流

```bash
# 创建功能分支
git checkout -b feature/knowledge-base-management

# 提交代码
git add .
git commit -m "feat: add knowledge base CRUD operations"

# 推送分支
git push origin feature/knowledge-base-management

# 创建 Pull Request
```

**提交信息规范**:
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 数据库操作

```bash
# 创建迁移
cd apps/backend
uv run alembic revision --autogenerate -m "Add knowledge base table"

# 执行迁移
uv run alembic upgrade head

# 回滚迁移
uv run alembic downgrade -1

# 查看迁移历史
uv run alembic history
```

### 测试

#### 前端测试

```bash
# 单元测试
npm run test:frontend

# 组件测试
npm run test:frontend -- --watch

# E2E 测试
npm run test:e2e
```

#### 后端测试

```bash
# 单元测试
cd apps/backend
uv run pytest

# 覆盖率测试
uv run pytest --cov=src --cov-report=html

# 特定测试
uv run pytest tests/test_knowledge_base.py
```

## 🔌 API 开发

### 添加新的API端点

1. **定义 Pydantic 模式** (`schemas/`)
```python
# schemas/knowledge_base.py
from pydantic import BaseModel
from typing import Optional, List

class KnowledgeBaseCreate(BaseModel):
    name: str
    description: Optional[str] = None
    tags: List[str] = []

class KnowledgeBaseResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    tags: List[str]
    created_at: datetime
```

2. **创建数据模型** (`models/`)
```python
# models/knowledge_base.py
from sqlalchemy import Column, String, Text, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from .base import BaseModel

class KnowledgeBase(BaseModel):
    __tablename__ = "knowledge_bases"
    
    name = Column(String(255), nullable=False)
    description = Column(Text)
    tags = Column(ARRAY(String), default=[])
```

3. **实现业务逻辑** (`services/`)
```python
# services/knowledge_base.py
from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from models.knowledge_base import KnowledgeBase
from schemas.knowledge_base import KnowledgeBaseCreate

class KnowledgeBaseService:
    async def create(
        self, 
        db: AsyncSession, 
        data: KnowledgeBaseCreate
    ) -> KnowledgeBase:
        kb = KnowledgeBase(**data.dict())
        db.add(kb)
        await db.commit()
        await db.refresh(kb)
        return kb
```

4. **添加API路由** (`api/routes/`)
```python
# api/routes/knowledge_base.py
from fastapi import APIRouter, Depends
from services.knowledge_base import KnowledgeBaseService
from schemas.knowledge_base import KnowledgeBaseCreate, KnowledgeBaseResponse

router = APIRouter(prefix="/knowledge-bases", tags=["knowledge-bases"])

@router.post("/", response_model=KnowledgeBaseResponse)
async def create_knowledge_base(
    data: KnowledgeBaseCreate,
    service: KnowledgeBaseService = Depends()
):
    return await service.create(data)
```

### 前端API集成

```typescript
// services/api/knowledge-base.ts
import { apiClient } from './client';
import type { KnowledgeBase, CreateKnowledgeBaseRequest } from '@/types';

export const knowledgeBaseApi = {
  async create(data: CreateKnowledgeBaseRequest): Promise<KnowledgeBase> {
    const response = await apiClient.post('/knowledge-bases', data);
    return response.data;
  },

  async list(params?: ListParams): Promise<PaginatedResponse<KnowledgeBase>> {
    const response = await apiClient.get('/knowledge-bases', { params });
    return response.data;
  },
};
```

## 🎨 UI 组件开发

### 组件结构

```typescript
// components/KnowledgeBaseCard.tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { KnowledgeBase } from '@/types';

interface KnowledgeBaseCardProps {
  knowledgeBase: KnowledgeBase;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
}

export function KnowledgeBaseCard({ 
  knowledgeBase, 
  onEdit, 
  onDelete 
}: KnowledgeBaseCardProps) {
  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <CardTitle>{knowledgeBase.name}</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-4">
          {knowledgeBase.description}
        </p>
        <div className="flex flex-wrap gap-2">
          {knowledgeBase.tags.map((tag) => (
            <Badge key={tag} variant="secondary">
              {tag}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
```

### 状态管理

```typescript
// hooks/useKnowledgeBases.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { knowledgeBaseApi } from '@/services/api';

export function useKnowledgeBases() {
  return useQuery({
    queryKey: ['knowledge-bases'],
    queryFn: () => knowledgeBaseApi.list(),
  });
}

export function useCreateKnowledgeBase() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: knowledgeBaseApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['knowledge-bases'] });
    },
  });
}
```

## 🐛 调试和故障排除

### 常见问题

1. **数据库连接失败**
```bash
# 检查数据库服务状态
docker-compose -f docker/docker-compose.dev.yml ps

# 查看数据库日志
docker-compose -f docker/docker-compose.dev.yml logs postgres
```

2. **前端构建失败**
```bash
# 清理缓存
rm -rf apps/frontend/.next
rm -rf apps/frontend/node_modules
npm install

# 检查类型错误
npm run type-check:frontend
```

3. **后端启动失败**
```bash
# 检查Python环境
cd apps/backend
uv run python --version

# 检查依赖
uv sync

# 查看详细错误
uv run python -m uvicorn src.education_rag_backend.main:app --reload --log-level debug
```

### 日志查看

```bash
# 查看所有服务日志
docker-compose -f docker/docker-compose.dev.yml logs -f

# 查看特定服务日志
docker-compose -f docker/docker-compose.dev.yml logs -f postgres
docker-compose -f docker/docker-compose.dev.yml logs -f redis
```

## 📦 部署

### 构建生产版本

```bash
# 构建所有应用
npm run build

# 构建Docker镜像
npm run docker:build

# 启动生产环境
npm run docker:up
```

### 环境变量配置

生产环境需要配置以下环境变量：

```bash
# 数据库
DATABASE_URL=postgresql+asyncpg://user:pass@host:5432/dbname
REDIS_URL=redis://host:6379/0

# 外部服务
PINECONE_API_KEY=your_production_key
OPENAI_API_KEY=your_production_key

# 安全
SECRET_KEY=your_production_secret_key
ALLOWED_HOSTS=your-domain.com,api.your-domain.com

# 存储
STORAGE_TYPE=s3
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret
```

---

*本开发指南将随着项目进展持续更新。*
