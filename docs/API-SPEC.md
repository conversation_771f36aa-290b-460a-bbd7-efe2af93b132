# Education RAG API 规范文档

## 📋 概述

本文档详细描述了Education RAG Management System的API接口规范，包括请求格式、响应格式、错误处理等。

## 🔗 基础信息

- **Base URL**: `https://api.education-rag.com/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **认证方式**: <PERSON><PERSON> (JWT)
- **API版本**: v1

## 🔐 认证

### JWT Token格式
```http
Authorization: Bearer <jwt_token>
```

### Token获取
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 1800
}
```

## 📊 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据
  },
  "message": "操作成功",
  "timestamp": "2025-01-15T10:30:00Z"
}
```

### 分页响应
```json
{
  "success": true,
  "data": [
    // 数据列表
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "pages": 5
  },
  "timestamp": "2025-01-15T10:30:00Z"
}
```

### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "name",
      "reason": "名称不能为空"
    }
  },
  "timestamp": "2025-01-15T10:30:00Z"
}
```

## 🗂️ 知识库API

### 获取知识库列表
```http
GET /knowledge-bases?page=1&limit=20&search=教育&sort=created_at&order=desc
```

**查询参数**:
- `page` (int): 页码，默认1
- `limit` (int): 每页数量，默认20，最大100
- `search` (string): 搜索关键词
- `sort` (string): 排序字段 (name, created_at, updated_at, document_count)
- `order` (string): 排序方向 (asc, desc)
- `tags` (string): 标签过滤，逗号分隔

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "kb_123456",
      "name": "教育资料库",
      "description": "包含各类教育相关文档",
      "tags": ["教育", "学习"],
      "document_count": 25,
      "total_size": 104857600,
      "owner": {
        "id": "user_123",
        "name": "张老师",
        "email": "<EMAIL>"
      },
      "settings": {
        "embedding_model": "text-embedding-ada-002",
        "chunk_size": 1000,
        "chunk_overlap": 200,
        "access_level": "private"
      },
      "created_at": "2025-01-10T08:00:00Z",
      "updated_at": "2025-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 5,
    "pages": 1
  }
}
```

### 创建知识库
```http
POST /knowledge-bases
Content-Type: application/json

{
  "name": "新知识库",
  "description": "知识库描述",
  "tags": ["标签1", "标签2"],
  "settings": {
    "embedding_model": "text-embedding-ada-002",
    "chunk_size": 1000,
    "chunk_overlap": 200,
    "access_level": "private",
    "auto_processing": true
  }
}
```

### 获取知识库详情
```http
GET /knowledge-bases/{id}
```

### 更新知识库
```http
PUT /knowledge-bases/{id}
Content-Type: application/json

{
  "name": "更新后的名称",
  "description": "更新后的描述",
  "tags": ["新标签"]
}
```

### 删除知识库
```http
DELETE /knowledge-bases/{id}
```

## 📄 文档管理API

### 上传文档
```http
POST /knowledge-bases/{id}/documents
Content-Type: multipart/form-data

files: [File1, File2, ...]
metadata: {
  "auto_process": true,
  "tags": ["重要", "教材"]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "upload_id": "upload_123456",
    "documents": [
      {
        "id": "doc_123456",
        "filename": "document.pdf",
        "original_name": "教学文档.pdf",
        "file_type": "application/pdf",
        "file_size": 2048576,
        "processing_status": "pending",
        "uploaded_at": "2025-01-15T10:30:00Z"
      }
    ]
  }
}
```

### 获取文档列表
```http
GET /knowledge-bases/{id}/documents?page=1&limit=20&status=completed&type=pdf
```

**查询参数**:
- `page`, `limit`: 分页参数
- `search`: 文档名称搜索
- `status`: 处理状态过滤 (pending, processing, completed, failed)
- `type`: 文件类型过滤 (pdf, docx, txt, etc.)
- `sort`: 排序字段 (name, size, uploaded_at)

### 获取文档详情
```http
GET /documents/{id}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "doc_123456",
    "knowledge_base_id": "kb_123456",
    "filename": "document.pdf",
    "original_name": "教学文档.pdf",
    "file_type": "application/pdf",
    "file_size": 2048576,
    "storage_path": "kb_123456/documents/doc_123456.pdf",
    "processing_status": "completed",
    "metadata": {
      "author": "张老师",
      "title": "教学文档",
      "page_count": 50,
      "language": "zh-CN"
    },
    "chunk_count": 25,
    "uploaded_at": "2025-01-15T10:30:00Z",
    "processed_at": "2025-01-15T10:35:00Z",
    "chunks": [
      {
        "id": "chunk_123456",
        "chunk_index": 0,
        "content": "文档内容片段...",
        "metadata": {
          "page": 1,
          "section": "引言"
        }
      }
    ]
  }
}
```

### 删除文档
```http
DELETE /documents/{id}
```

### 重新处理文档
```http
POST /documents/{id}/reprocess
Content-Type: application/json

{
  "settings": {
    "chunk_size": 800,
    "chunk_overlap": 150
  }
}
```

## 🔍 检索测试API

### 执行检索查询
```http
POST /knowledge-bases/{id}/search
Content-Type: application/json

{
  "query": "什么是机器学习？",
  "top_k": 10,
  "threshold": 0.7,
  "filters": {
    "document_types": ["pdf", "docx"],
    "date_range": {
      "start": "2024-01-01",
      "end": "2025-01-15"
    },
    "tags": ["机器学习", "AI"]
  },
  "rerank": true,
  "hybrid_search": false
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "query_id": "query_123456",
    "results": [
      {
        "id": "result_123456",
        "chunk_id": "chunk_123456",
        "content": "机器学习是人工智能的一个分支...",
        "relevance_score": 0.95,
        "rank_position": 1,
        "document": {
          "id": "doc_123456",
          "name": "机器学习基础.pdf",
          "page": 5
        },
        "metadata": {
          "section": "第一章 概述",
          "highlight": "机器学习是人工智能的一个分支"
        }
      }
    ],
    "metadata": {
      "total_results": 10,
      "search_time": 0.25,
      "embedding_time": 0.05,
      "retrieval_time": 0.15,
      "rerank_time": 0.05
    }
  }
}
```

### 保存查询会话
```http
POST /search-sessions
Content-Type: application/json

{
  "knowledge_base_id": "kb_123456",
  "query": "什么是机器学习？",
  "results": [
    // 检索结果数组
  ],
  "search_params": {
    "top_k": 10,
    "threshold": 0.7
  },
  "feedback": {
    "rating": 4,
    "relevant_results": [1, 2, 3],
    "irrelevant_results": [8, 9],
    "comments": "结果很准确"
  }
}
```

## 📊 分析统计API

### 获取知识库统计
```http
GET /knowledge-bases/{id}/stats?period=7d
```

### 获取检索分析
```http
GET /knowledge-bases/{id}/search-analytics?start_date=2025-01-01&end_date=2025-01-15
```

## ⚠️ 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `FORBIDDEN` | 403 | 权限不足 |
| `NOT_FOUND` | 404 | 资源不存在 |
| `VALIDATION_ERROR` | 422 | 请求参数验证失败 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `INTERNAL_ERROR` | 500 | 服务器内部错误 |
| `SERVICE_UNAVAILABLE` | 503 | 服务暂时不可用 |

## 🔄 WebSocket API

### 文档处理状态订阅
```javascript
const ws = new WebSocket('wss://api.education-rag.com/v1/ws/documents/doc_123456/status');

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Processing update:', data);
};
```

**消息格式**:
```json
{
  "type": "processing_update",
  "document_id": "doc_123456",
  "status": "processing",
  "progress": 65,
  "stage": "embedding_generation",
  "message": "正在生成嵌入向量...",
  "timestamp": "2025-01-15T10:35:00Z"
}
```

---

*本API规范将随着系统开发持续更新。*
