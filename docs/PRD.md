# Education RAG Management System - 产品需求文档 (PRD)

## 📖 1. 产品概述

### 1.1 产品定位
Education RAG Management System 是一个面向教育机构、研究人员和知识工作者的综合性RAG（检索增强生成）管理平台，提供知识库管理、文档处理和检索测试的完整解决方案。

### 1.2 目标用户
- **主要用户**：教育机构的教师、研究人员、内容管理员
- **次要用户**：学生、知识工作者、企业培训师
- **管理员**：系统管理员、IT运维人员

### 1.3 核心价值主张
- 🎯 **简化知识管理**：直观的界面管理多个知识库
- 🚀 **高效文档处理**：支持多种格式的智能文档解析
- 🔍 **精准检索测试**：实时测试和优化检索质量
- 📊 **数据驱动优化**：详细的分析和性能指标

### 1.4 技术架构概览
- **前端**：Next.js 14 + TypeScript + Tailwind CSS
- **后端**：Python FastAPI + uv包管理
- **向量数据库**：Pinecone
- **关系数据库**：PostgreSQL + pgvector
- **缓存**：Redis
- **RAG框架**：LangGraph
- **文件存储**：S3兼容存储
- **部署**：Docker + Kubernetes

## 🎯 2. 详细功能需求

### 2.1 知识库管理模块

#### 2.1.1 知识库CRUD操作
**功能描述**：用户可以创建、查看、编辑和删除知识库

**详细需求**：
- **创建知识库**
  - 必填字段：名称、描述
  - 可选字段：标签、访问权限、嵌入模型选择
  - 支持模板选择（教育、研究、企业等预设模板）
  - 实时验证名称唯一性

- **知识库列表视图**
  - 卡片式布局展示所有知识库
  - 显示信息：名称、描述、文档数量、创建时间、最后更新时间
  - 支持按名称、创建时间、文档数量排序
  - 支持搜索和标签筛选

- **知识库详情页**
  - 基本信息编辑
  - 文档管理入口
  - 检索测试入口
  - 使用统计和分析

#### 2.1.2 权限管理
**功能描述**：基于角色的访问控制

**权限级别**：
- **所有者**：完全控制权限
- **编辑者**：可以上传、删除文档，修改知识库设置
- **查看者**：只能查看和使用检索功能
- **访客**：受限的查看权限

**详细需求**：
- 支持邀请用户加入知识库
- 支持权限转移和撤销
- 审计日志记录所有权限变更

### 2.2 文档管理模块

#### 2.2.1 文档上传功能
**功能描述**：支持多种格式文档的批量上传

**支持格式**：
- **文档类型**：PDF, DOCX, DOC, TXT, MD, RTF
- **图片类型**：PNG, JPG, JPEG（OCR处理）
- **压缩包**：ZIP（自动解压处理）

**上传方式**：
- 拖拽上传
- 点击选择文件
- 批量上传（最多50个文件）
- URL导入（支持公开链接）

**上传限制**：
- 单文件最大100MB
- 总上传量限制（根据用户套餐）
- 文件类型验证
- 病毒扫描

#### 2.2.2 文档处理流程
**功能描述**：自动化的文档解析和预处理

**处理步骤**：
1. **文件验证**：格式检查、大小验证、安全扫描
2. **内容提取**：文本提取、表格识别、图片OCR
3. **文档分块**：智能分块策略、重叠处理
4. **嵌入生成**：向量化处理、存储到Pinecone
5. **索引建立**：元数据索引、全文搜索索引

**处理状态**：
- `pending`：等待处理
- `processing`：正在处理
- `completed`：处理完成
- `failed`：处理失败
- `retrying`：重试中

#### 2.2.3 文档管理界面
**功能描述**：直观的文档管理界面

**列表视图功能**：
- 文档缩略图预览
- 文件信息：名称、大小、类型、上传时间
- 处理状态指示器
- 批量操作：删除、重新处理、导出
- 搜索和筛选功能

**文档详情功能**：
- 文档预览（支持PDF、图片、文本）
- 分块结果查看
- 元数据编辑
- 处理日志查看
- 重新处理选项

### 2.3 检索测试模块

#### 2.3.1 查询测试界面
**功能描述**：实时测试检索质量的交互界面

**界面布局**：
- **查询输入区**：
  - 多行文本输入框
  - 查询历史记录
  - 预设查询模板
  - 查询参数配置

- **结果展示区**：
  - 检索结果列表
  - 相关性评分显示
  - 源文档高亮
  - 结果排序选项

- **参数配置区**：
  - 检索数量设置（top-k）
  - 相似度阈值
  - 检索策略选择
  - 过滤条件设置

#### 2.3.2 检索结果分析
**功能描述**：详细的检索结果分析和评估

**结果展示**：
- **分块内容**：完整的检索文本
- **相关性评分**：数值和可视化展示
- **源文档信息**：文档名称、页码、位置
- **上下文信息**：前后文内容预览

**分析功能**：
- 结果相关性人工标注
- 查询-结果对保存
- 检索质量评估指标
- A/B测试支持

#### 2.3.3 性能监控
**功能描述**：检索性能的实时监控和历史分析

**监控指标**：
- **响应时间**：平均、P95、P99响应时间
- **检索准确率**：基于人工标注的准确率
- **用户满意度**：用户反馈评分
- **系统负载**：并发查询数、资源使用率

## 🎨 3. 用户界面设计规范

### 3.1 设计原则
- **简洁性**：界面简洁，减少认知负担
- **一致性**：统一的设计语言和交互模式
- **可访问性**：支持键盘导航和屏幕阅读器
- **响应式**：适配桌面、平板、手机设备

### 3.2 主要页面布局

#### 3.2.1 仪表板页面
```
┌─────────────────────────────────────────────────────────┐
│ Header: Logo | Navigation | User Menu                   │
├─────────────────────────────────────────────────────────┤
│ Breadcrumb: Dashboard                                   │
├─────────────────────────────────────────────────────────┤
│ Stats Cards: Total KBs | Documents | Queries | Users   │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────┐ │
│ │ Recent Activity │ │ Quick Actions   │ │ System      │ │
│ │                 │ │                 │ │ Status      │ │
│ │ - KB Created    │ │ + New KB        │ │             │ │
│ │ - Doc Uploaded  │ │ + Upload Doc    │ │ ● All Good  │ │
│ │ - Query Made    │ │ + Test Query    │ │             │ │
│ └─────────────────┘ └─────────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 3.2.2 知识库列表页面
```
┌─────────────────────────────────────────────────────────┐
│ Header + Breadcrumb: Dashboard > Knowledge Bases       │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ [Search] [Filter] [+]  │
│ │ KB Card 1   │ │ KB Card 2   │                        │
│ │ ┌─────────┐ │ │ ┌─────────┐ │                        │
│ │ │ Icon    │ │ │ │ Icon    │ │                        │
│ │ └─────────┘ │ │ └─────────┘ │                        │
│ │ Name        │ │ │ Name        │                        │
│ │ Description │ │ │ Description │                        │
│ │ 📄 25 docs  │ │ │ 📄 12 docs  │                        │
│ │ 🕒 2d ago   │ │ │ 🕒 1w ago   │                        │
│ └─────────────┘ └─────────────┘                        │
└─────────────────────────────────────────────────────────┘
```

#### 3.2.3 文档管理页面
```
┌─────────────────────────────────────────────────────────┐
│ Header + Breadcrumb: KB > Documents                    │
├─────────────────────────────────────────────────────────┤
│ [Upload] [Bulk Actions] [Search] [Filter] [View Mode]  │
├─────────────────────────────────────────────────────────┤
│ ┌──────────────────────────────────────────────────────┐ │
│ │ Document List/Grid View                              │ │
│ │ ┌─────┐ filename.pdf    📄 2.5MB  ✅ Processed      │ │
│ │ │ 📄  │ Uploaded: 2h ago                            │ │
│ │ └─────┘ 📊 25 chunks    👁️ Preview  🗑️ Delete      │ │
│ │                                                      │ │
│ │ ┌─────┐ document.docx   📄 1.2MB  🔄 Processing     │ │
│ │ │ 📄  │ Uploaded: 5m ago                            │ │
│ │ └─────┘ Progress: ████████░░ 80%                     │ │
│ └──────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 3.3 交互设计规范

#### 3.3.1 状态反馈
- **加载状态**：骨架屏 + 进度指示器
- **成功状态**：绿色提示 + 图标
- **错误状态**：红色提示 + 错误信息
- **警告状态**：黄色提示 + 建议操作

#### 3.3.2 操作确认
- **危险操作**：二次确认弹窗
- **批量操作**：操作预览 + 确认
- **不可逆操作**：输入确认文本

## 🔌 4. API接口设计

### 4.1 RESTful API规范

#### 4.1.1 基础规范
- **Base URL**: `https://api.education-rag.com/v1`
- **认证方式**: Bearer Token (JWT)
- **数据格式**: JSON
- **HTTP状态码**: 标准RESTful状态码

#### 4.1.2 知识库API
```typescript
// 获取知识库列表
GET /knowledge-bases
Query: page, limit, search, sort, filter
Response: {
  data: KnowledgeBase[],
  pagination: PaginationInfo,
  total: number
}

// 创建知识库
POST /knowledge-bases
Body: {
  name: string,
  description: string,
  tags?: string[],
  settings?: KnowledgeBaseSettings
}

// 获取知识库详情
GET /knowledge-bases/{id}
Response: KnowledgeBase

// 更新知识库
PUT /knowledge-bases/{id}
Body: Partial<KnowledgeBase>

// 删除知识库
DELETE /knowledge-bases/{id}
```

#### 4.1.3 文档管理API
```typescript
// 上传文档
POST /knowledge-bases/{id}/documents
Content-Type: multipart/form-data
Body: {
  files: File[],
  metadata?: DocumentMetadata
}

// 获取文档列表
GET /knowledge-bases/{id}/documents
Query: page, limit, search, status, type

// 获取文档详情
GET /documents/{id}
Response: Document

// 删除文档
DELETE /documents/{id}

// 重新处理文档
POST /documents/{id}/reprocess
```

#### 4.1.4 检索测试API
```typescript
// 执行检索查询
POST /knowledge-bases/{id}/search
Body: {
  query: string,
  top_k?: number,
  threshold?: number,
  filters?: SearchFilters
}
Response: {
  results: SearchResult[],
  metadata: SearchMetadata
}

// 保存查询结果
POST /search-sessions
Body: {
  knowledge_base_id: string,
  query: string,
  results: SearchResult[],
  feedback?: UserFeedback
}
```

### 4.2 WebSocket API
```typescript
// 文档处理状态更新
WS /ws/documents/{id}/status
Message: {
  type: 'processing_update',
  document_id: string,
  status: ProcessingStatus,
  progress?: number,
  error?: string
}

// 实时检索结果
WS /ws/knowledge-bases/{id}/search
Message: {
  type: 'search_result',
  query_id: string,
  results: SearchResult[]
}
```

## 📊 5. 数据模型设计

### 5.1 核心实体关系
详见项目中的数据模型图表。

### 5.2 详细数据类型定义

```typescript
// 枚举类型定义
enum ProcessingStatus {
  PENDING = "pending",
  PROCESSING = "processing",
  COMPLETED = "completed",
  FAILED = "failed",
  RETRYING = "retrying"
}

enum PermissionLevel {
  OWNER = "owner",
  EDITOR = "editor",
  VIEWER = "viewer",
  GUEST = "guest"
}

enum LogLevel {
  DEBUG = "debug",
  INFO = "info",
  WARNING = "warning",
  ERROR = "error"
}

// 设置类型定义
interface KnowledgeBaseSettings {
  embedding_model: string;
  chunk_size: number;
  chunk_overlap: number;
  retrieval_strategy: string;
  access_level: "public" | "private" | "restricted";
  auto_processing: boolean;
}

interface DocumentMetadata {
  author?: string;
  title?: string;
  subject?: string;
  keywords?: string[];
  language?: string;
  page_count?: number;
  word_count?: number;
  created_date?: string;
  modified_date?: string;
}

interface SearchParams {
  top_k: number;
  threshold: number;
  filters?: {
    document_types?: string[];
    date_range?: {
      start: string;
      end: string;
    };
    tags?: string[];
  };
  rerank?: boolean;
  hybrid_search?: boolean;
}
```

## 🔄 6. 用户体验流程

### 6.1 新用户引导流程
1. **用户注册/登录** → 欢迎页面
2. **创建第一个知识库** → 知识库配置向导
3. **上传示例文档** → 等待文档处理
4. **体验检索功能** → 查看结果分析
5. **完成引导** → 开始正式使用

### 6.2 文档处理流程
1. **选择文件** → 文件验证
2. **开始上传** → 上传进度显示
3. **文件解析** → 内容提取
4. **文档分块** → 生成嵌入向量
5. **存储到向量数据库** → 更新索引
6. **处理完成** → 发送通知

### 6.3 检索测试流程
1. **输入查询** → 参数配置
2. **执行检索** → 结果展示
3. **结果分析** → 相关性评估
4. **参数调优** → 重新测试
5. **保存配置** → 应用到生产

## ⚡ 7. 性能要求

### 7.1 响应时间要求
- **页面加载**：首屏 < 2秒，完整加载 < 5秒
- **文档上传**：支持断点续传，显示实时进度
- **检索查询**：< 3秒返回结果
- **文档处理**：根据文档大小，提供预估时间

### 7.2 并发性能
- **用户并发**：支持1000+并发用户
- **文档处理**：支持100个文档同时处理
- **检索并发**：支持500+并发查询

### 7.3 存储要求
- **文档存储**：支持TB级别文档存储
- **向量存储**：高效的向量检索性能
- **数据备份**：自动备份和恢复机制

### 7.4 可扩展性
- **水平扩展**：支持多实例部署
- **负载均衡**：智能请求分发
- **缓存策略**：多层缓存优化

## 🔒 8. 安全要求

### 8.1 身份认证
- **多因素认证**：支持2FA
- **SSO集成**：支持SAML、OAuth2
- **会话管理**：安全的会话控制
- **密码策略**：强密码要求

### 8.2 数据安全
- **数据加密**：传输和存储加密
- **访问控制**：细粒度权限管理
- **审计日志**：完整的操作记录
- **数据脱敏**：敏感信息保护

### 8.3 文件安全
- **病毒扫描**：上传文件安全检查
- **内容过滤**：敏感信息检测
- **访问限制**：基于角色的文件访问
- **文件完整性**：校验和验证

### 8.4 网络安全
- **HTTPS强制**：所有通信加密
- **CORS配置**：跨域请求控制
- **Rate Limiting**：API调用频率限制
- **DDoS防护**：攻击检测和防护

## 📈 9. 分析和监控

### 9.1 用户行为分析
- **使用统计**：功能使用频率分析
- **用户路径**：用户操作流程分析
- **性能监控**：用户体验指标
- **转化分析**：功能使用转化率

### 9.2 系统监控
- **性能指标**：响应时间、吞吐量
- **错误监控**：错误率、异常追踪
- **资源监控**：CPU、内存、存储使用
- **服务健康**：服务可用性监控

### 9.3 业务指标
- **知识库增长**：创建和使用趋势
- **文档处理**：成功率和效率
- **检索质量**：准确率和用户满意度
- **用户活跃度**：DAU、MAU、留存率

## 🚀 10. 部署和运维

### 10.1 部署架构
- **容器化部署**：Docker + Kubernetes
- **微服务架构**：服务拆分和独立部署
- **CI/CD流水线**：自动化构建和部署
- **环境管理**：开发、测试、生产环境

### 10.2 监控告警
- **实时监控**：系统状态实时监控
- **告警机制**：异常情况及时通知
- **日志聚合**：集中化日志管理
- **性能分析**：性能瓶颈识别

### 10.3 备份恢复
- **数据备份**：定期自动备份
- **灾难恢复**：快速恢复机制
- **版本管理**：数据版本控制
- **测试验证**：备份有效性验证

## 📋 11. 开发计划

### 11.1 里程碑规划
- **Phase 1**：基础架构和用户管理（4周）
- **Phase 2**：知识库管理功能（6周）
- **Phase 3**：文档处理和存储（8周）
- **Phase 4**：检索测试功能（6周）
- **Phase 5**：分析监控和优化（4周）

### 11.2 技术债务管理
- **代码质量**：持续重构和优化
- **性能优化**：定期性能评估
- **安全更新**：及时安全补丁
- **依赖管理**：第三方库版本管理

### 11.3 质量保证
- **单元测试**：代码覆盖率 > 80%
- **集成测试**：API和功能测试
- **性能测试**：负载和压力测试
- **安全测试**：安全漏洞扫描

---

## 📚 附录

### A. 术语表
- **RAG**: Retrieval-Augmented Generation，检索增强生成
- **KB**: Knowledge Base，知识库
- **Embedding**: 嵌入向量，文本的数值化表示
- **Chunk**: 文档分块，文档的片段单元

### B. 参考资料
- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
- [Pinecone Vector Database](https://www.pinecone.io/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Next.js Documentation](https://nextjs.org/docs)

### C. 变更记录
- **v1.0** (2025-01-15): 初始版本
- 后续版本将记录重要变更

---

*本文档将随着项目进展持续更新和完善。*
