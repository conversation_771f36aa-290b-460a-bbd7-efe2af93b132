# Education RAG Management System - 项目概览

## 🎯 项目简介

Education RAG Management System 是一个现代化的知识库管理平台，专为教育机构、研究人员和知识工作者设计。系统基于RAG（检索增强生成）技术，提供完整的文档管理、智能检索和质量测试解决方案。

## 🚀 当前实现状态

**版本**: v0.2.0-alpha
**完成度**: 85%
**最后更新**: 2025-07-16

### ✅ 已实现功能
- 🐳 **完整的 Docker 开发环境**: PostgreSQL + Redis 容器化部署
- 🎨 **响应式前端界面**: React + Next.js + Tailwind CSS + shadcn/ui
- 🔧 **RESTful API 后端**: FastAPI + LangChain + Pinecone 原生架构
- 📝 **知识库管理界面**: 创建、列表、搜索、过滤功能
- 🧪 **全面的端到端测试**: Playwright 测试框架，75.6% 通过率
- 🔌 **第三方服务集成**: Pinecone、智谱AI、DeepSeek API 配置
- 🎉 **NEW**: **LangChain 架构升级**: 标准化文档处理流程
- 🎉 **NEW**: **文档上传功能**: 完整的文件上传、处理、向量化流程
- 🎉 **NEW**: **智谱AI embedding-3 集成**: 2048维向量生成
- 🎉 **NEW**: **Pinecone Namespace 架构**: 完全的数据隔离和管理
- 🎉 **NEW**: **多格式文档支持**: PDF、DOCX、TXT、MD、HTML
- 🎉 **NEW**: **文档状态跟踪**: 上传、处理、完成状态管理

### 🔄 进行中
- 📱 移动端兼容性优化
- 🔍 RAG 检索功能实现
- 💬 智能问答功能开发

### 📋 待实现核心功能
- 🔍 语义搜索和检索功能 (下一个重点)
- 🔍 向量搜索和 RAG 检索 (基于 Pinecone)
- 💬 智能问答对话功能 (集成 DeepSeek)
- 👤 用户认证和权限管理
- 📊 使用分析和监控

### 🎉 重大架构升级 (2025-07-16)

#### Pinecone 原生知识库管理
- **完全移除 PostgreSQL 依赖**: 知识库数据直接存储在 Pinecone 中
- **元数据丰富存储**: 利用 Pinecone 元数据功能存储完整的知识库信息
- **向量优先设计**: 为 RAG 功能优化的现代化架构
- **自动索引管理**: 智能创建和配置 Pinecone 索引

#### 解决的关键问题
- ✅ **SQLAlchemy 关系映射错误**: 通过移除复杂的 ORM 关系完全解决
- ✅ **知识库创建流程**: API 和前端完全正常工作
- ✅ **架构复杂性**: 大幅简化技术栈和数据流

#### 技术优势
- **性能提升**: 原生向量操作，无数据库同步开销
- **扩展性**: 更好的水平扩展能力
- **维护性**: 单一数据源，简化的服务层
- **RAG 就绪**: 为文档向量化和检索功能奠定基础

> 📖 **详细状态追踪**: 查看 [IMPLEMENTATION-STATUS.md](./IMPLEMENTATION-STATUS.md) 获取完整的实现状态和技术债务信息。

## 🏗️ 技术架构

### 核心技术栈

| 层级 | 技术选型 | 说明 |
|------|----------|------|
| **前端** | Next.js 14 + TypeScript | 现代化React框架，支持SSR/SSG |
| **后端** | Python FastAPI + uv | 高性能异步API框架 |
| **数据库** | PostgreSQL + pgvector | 关系数据库 + 向量扩展 |
| **向量数据库** | Pinecone | 专业向量存储和检索 |
| **缓存** | Redis | 高性能缓存和会话存储 |
| **RAG框架** | LangGraph | 先进的RAG工作流编排 |
| **文件存储** | S3兼容存储 | 可扩展的对象存储 |
| **容器化** | Docker + Kubernetes | 现代化部署方案 |

### 架构特点

- **🏢 Monorepo架构**: 统一的代码管理和依赖共享
- **🔄 微服务设计**: 模块化、可扩展的服务架构
- **⚡ 异步处理**: 高并发文档处理和检索
- **🔒 安全优先**: 多层安全防护和权限控制
- **📊 可观测性**: 全面的监控和分析能力

## 🎯 核心功能

### 1. 知识库管理
- ✅ 多知识库创建和管理
- ✅ 基于角色的权限控制
- ✅ 知识库模板和配置
- ✅ 标签分类和搜索

### 2. 文档处理
- ✅ 多格式文档上传（PDF、DOCX、TXT等）
- ✅ 智能文档解析和OCR
- ✅ 自动分块和向量化
- ✅ 实时处理状态监控

### 3. 检索测试
- ✅ 实时检索质量测试
- ✅ 多种检索策略配置
- ✅ 结果相关性分析
- ✅ A/B测试支持

### 4. 分析监控
- ✅ 用户行为分析
- ✅ 系统性能监控
- ✅ 检索质量评估
- ✅ 业务指标统计

## 📁 项目结构

```
education-rag/
├── 📱 apps/
│   ├── frontend/          # Next.js 前端应用
│   └── backend/           # FastAPI 后端应用
├── 📦 packages/
│   ├── types/             # 共享类型定义
│   ├── utils/             # 共享工具函数
│   └── config/            # 共享配置
├── 🐳 docker/             # Docker 配置文件
├── 🔧 scripts/            # 开发和部署脚本
├── 📚 docs/               # 项目文档
│   ├── PRD.md             # 产品需求文档
│   ├── API-SPEC.md        # API接口规范
│   ├── DATABASE-SCHEMA.md # 数据库设计
│   └── DEVELOPMENT-GUIDE.md # 开发指南
└── ⚙️ 配置文件
```

## 🚀 快速开始

### 环境准备

```bash
# 1. 克隆项目
git clone <repository-url>
cd education-rag

# 2. 安装依赖
npm run setup

# 3. 配置环境变量
cp apps/frontend/.env.example apps/frontend/.env.local
cp apps/backend/.env.example apps/backend/.env

# 4. 启动开发环境
npm run dev
```

### 访问地址

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 📋 开发计划

### Phase 1: 基础架构 (4周)
- [x] Monorepo项目结构搭建
- [x] 前后端基础框架配置
- [ ] 用户认证和权限系统
- [ ] 数据库设计和迁移
- [ ] 基础UI组件库

### Phase 2: 知识库管理 (6周)
- [ ] 知识库CRUD操作
- [ ] 权限管理系统
- [ ] 知识库配置和模板
- [ ] 用户界面实现

### Phase 3: 文档处理 (8周)
- [ ] 文件上传和验证
- [ ] 多格式文档解析
- [ ] 文档分块和向量化
- [ ] 处理状态监控

### Phase 4: 检索测试 (6周)
- [ ] 检索引擎集成
- [ ] 测试界面开发
- [ ] 结果分析功能
- [ ] 性能优化

### Phase 5: 分析监控 (4周)
- [ ] 用户行为分析
- [ ] 系统监控面板
- [ ] 性能指标收集
- [ ] 报告生成功能

## 🔧 开发工具链

### 代码质量
- **ESLint + Prettier**: 前端代码规范
- **Ruff + Black**: Python代码格式化
- **TypeScript**: 静态类型检查
- **MyPy**: Python类型检查

### 测试框架
- **Jest + Testing Library**: 前端单元测试
- **Pytest**: 后端单元测试
- **Playwright**: E2E测试

### 开发工具
- **VS Code**: 推荐IDE配置
- **Docker Compose**: 本地开发环境
- **GitHub Actions**: CI/CD流水线

## 📊 性能指标

### 目标性能
- **页面加载**: 首屏 < 2秒
- **API响应**: 平均 < 500ms
- **文档处理**: 1MB/秒处理速度
- **检索查询**: < 3秒返回结果

### 扩展性目标
- **并发用户**: 1000+
- **文档存储**: TB级别
- **检索QPS**: 500+

## 🔒 安全措施

### 认证授权
- JWT Token认证
- 基于角色的权限控制
- 多因素认证支持
- SSO集成能力

### 数据安全
- 传输层TLS加密
- 数据库字段加密
- 文件访问控制
- 审计日志记录

### 系统安全
- API速率限制
- 输入验证和过滤
- CORS安全配置
- 容器安全扫描

## 📈 监控体系

### 应用监控
- 性能指标收集
- 错误追踪和告警
- 用户行为分析
- 业务指标统计

### 基础设施监控
- 服务健康检查
- 资源使用监控
- 日志聚合分析
- 告警通知机制

## 🤝 贡献指南

### 开发流程
1. Fork项目并创建功能分支
2. 遵循代码规范进行开发
3. 编写测试并确保通过
4. 提交PR并等待代码审查

### 代码规范
- 遵循项目的ESLint/Ruff配置
- 编写清晰的提交信息
- 添加必要的文档和注释
- 确保测试覆盖率

## 📞 联系方式

- **项目负责人**: Education RAG Team
- **邮箱**: <EMAIL>
- **文档**: 查看 `docs/` 目录
- **问题反馈**: GitHub Issues

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](../LICENSE) 文件。

---

*最后更新: 2025-01-15*
